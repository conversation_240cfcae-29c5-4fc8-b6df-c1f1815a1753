// 等离子体技术演示常量配置

import {
  Material,
  Process,
  ContactAngleData,
  DemoConfig,
  MaterialType,
  ProcessType
} from '@/types/plasma';

// 材料配置
export const MATERIALS: Material[] = [
  {
    id: MaterialType.POLYMER,
    name: 'polymer',
    displayName: '聚合物',
    description: 'PP、PC等塑料材料',
    color: '#4CAF50',
    roughness: 0.1,
    metalness: 0.0,
    modelPath: '/models/polymer_sample.glb'
  },
  {
    id: MaterialType.METAL,
    name: 'metal',
    displayName: '金属',
    description: '铝、不锈钢等金属',
    color: '#9E9E9E',
    roughness: 0.3,
    metalness: 1.0,
    modelPath: '/models/metal_sample.glb'
  },
  {
    id: MaterialType.GLASS_CERAMIC,
    name: 'glass_ceramic',
    displayName: '玻璃陶瓷',
    description: '玻璃、陶瓷等无机材料',
    color: '#2196F3',
    roughness: 0.1,
    metalness: 0.0,
    modelPath: '/models/glass_sample.glb'
  }
];

// 工艺配置 - 针对不同材料的差异化参数
export const PROCESSES: Process[] = [
  {
    id: ProcessType.ACTIVATION,
    name: 'activation',
    displayName: '表面活化',
    description: '提高表面能，改善润湿性和粘接性能',
    color: '#9C27B0',
    duration: 30.0,
    parameters: {
      power: 100,
      gasFlow: 50,
      speed: 10,
      gas: 'O₂/Ar',
      pressure: 0.5,
      temperature: 25
    }
  },
  {
    id: ProcessType.ETCHING,
    name: 'etching',
    displayName: '等离子刻蚀',
    description: '去除氧化层和污染物，增加表面粗糙度',
    color: '#FF5722',
    duration: 45.0,
    parameters: {
      power: 150,
      gasFlow: 80,
      speed: 8,
      gas: 'Ar/CF₄',
      pressure: 0.8,
      temperature: 35
    }
  },
  {
    id: ProcessType.COATING,
    name: 'coating',
    displayName: '等离子涂层',
    description: '在表面沉积功能性薄膜层',
    color: '#00BCD4',
    duration: 60.0,
    parameters: {
      power: 120,
      gasFlow: 60,
      speed: 6,
      gas: 'SiH₄/N₂',
      pressure: 1.2,
      temperature: 45
    }
  }
];

// 水滴角测试数据
export const CONTACT_ANGLE_DATA: ContactAngleData[] = [
  {
    material: MaterialType.POLYMER,
    before: 95,
    after: 25
  },
  {
    material: MaterialType.METAL,
    before: 85,
    after: 15
  },
  {
    material: MaterialType.GLASS_CERAMIC,
    before: 75,
    after: 8
  }
];

// 演示配置
export const DEMO_CONFIG: DemoConfig = {
  scene: {
    background: '#f0f0f0',
    fog: {
      color: '#f0f0f0',
      near: 10,
      far: 100
    }
  },
  camera: {
    position: [5, 5, 5],
    target: [0, 0, 0],
    fov: 75,
    near: 0.1,
    far: 1000
  },
  lights: {
    ambient: {
      color: '#ffffff',
      intensity: 0.4
    },
    directional: {
      color: '#ffffff',
      intensity: 0.8,
      position: [10, 10, 5]
    },
    point: {
      color: '#ffffff',
      intensity: 0.6,
      position: [0, 5, 0]
    }
  },
  models: {
    workpiece: {
      position: [0, 0, 0],
      rotation: [0, 0, 0],
      scale: [1, 1, 1]
    },
    plasmaGun: {
      position: [0, 3, 0],
      rotation: [-Math.PI / 2, 0, 0],
      scale: [1, 1, 1]
    }
  },
  particles: {
    plasma: {
      count: 1000,
      color: '#00ffff',
      size: 0.1,
      speed: 2.0,
      lifetime: 2.0,
      emissionRate: 100
    },
    debris: {
      count: 500,
      color: '#ffaa00',
      size: 0.05,
      speed: 1.0,
      lifetime: 1.5,
      emissionRate: 50
    }
  },
  animation: {
    defaultDuration: 2.0,
    easing: 'easeInOutQuad'
  }
};

// 默认参数范围
export const PARAMETER_RANGES = {
  power: { min: 50, max: 300, unit: 'W' },
  gasFlow: { min: 10, max: 200, unit: 'sccm' },
  speed: { min: 20, max: 200, unit: 'mm/min' },
  pressure: { min: 1, max: 100, unit: 'Pa' },
  temperature: { min: 20, max: 200, unit: '°C' }
};

// 动画时间配置
export const ANIMATION_TIMINGS = {
  materialSwitch: 1.0,
  processStart: 0.5,
  plasmaIgnition: 0.8,
  surfaceEffect: 2.0,
  contactAngleTest: 3.0,
  parameterUpdate: 0.1
};

// 颜色主题
export const THEME_COLORS = {
  primary: '#9C27B0',
  secondary: '#00BCD4',
  accent: '#FF5722',
  background: '#1a1a2e',
  surface: '#16213e',
  text: '#ffffff',
  textSecondary: '#b0b0b0',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336'
};

// UI配置
export const UI_CONFIG = {
  sidebar: {
    width: 280,
    background: 'rgba(22, 33, 62, 0.9)',
    backdropFilter: 'blur(10px)'
  },
  panel: {
    borderRadius: 12,
    padding: 16,
    margin: 8
  },
  button: {
    height: 48,
    borderRadius: 8,
    fontSize: 14
  },
  card: {
    borderRadius: 16,
    padding: 20,
    shadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
  }
};

// 3D场景配置
export const SCENE_CONFIG = {
  controls: {
    enableDamping: true,
    dampingFactor: 0.05,
    enableZoom: true,
    enablePan: true,
    enableRotate: true,
    maxDistance: 20,
    minDistance: 2,
    maxPolarAngle: Math.PI / 2
  },
  renderer: {
    antialias: true,
    alpha: true,
    shadowMap: true,
    toneMapping: 'ACESFilmicToneMapping',
    toneMappingExposure: 1.0
  }
};

// 性能配置
export const PERFORMANCE_CONFIG = {
  targetFPS: 60,
  maxParticles: 2000,
  lodLevels: [
    { distance: 5, quality: 'high' },
    { distance: 15, quality: 'medium' },
    { distance: 30, quality: 'low' }
  ],
  enableStats: process.env.NODE_ENV === 'development'
};

// 材料特定的工艺参数配置
export const MATERIAL_SPECIFIC_PARAMETERS = {
  [MaterialType.POLYMER]: {
    [ProcessType.ACTIVATION]: {
      power: 80,
      gasFlow: 40,
      speed: 12,
      gas: 'O₂/Ar',
      pressure: 0.3,
      temperature: 20,
      effectIntensity: 0.8,
      animationSpeed: 1.0,
      particleColor: '#4CAF50',
      surfaceChangeRate: 0.7
    },
    [ProcessType.ETCHING]: {
      power: 120,
      gasFlow: 60,
      speed: 10,
      gas: 'O₂/CF₄',
      pressure: 0.6,
      temperature: 30,
      effectIntensity: 0.9,
      animationSpeed: 1.2,
      particleColor: '#FF5722',
      surfaceChangeRate: 0.8
    },
    [ProcessType.COATING]: {
      power: 100,
      gasFlow: 50,
      speed: 8,
      gas: 'SiH₄/N₂',
      pressure: 1.0,
      temperature: 40,
      effectIntensity: 0.7,
      animationSpeed: 0.8,
      particleColor: '#00BCD4',
      surfaceChangeRate: 0.6
    }
  },
  [MaterialType.METAL]: {
    [ProcessType.ACTIVATION]: {
      power: 150,
      gasFlow: 70,
      speed: 8,
      gas: 'Ar/H₂',
      pressure: 0.8,
      temperature: 35,
      effectIntensity: 1.0,
      animationSpeed: 1.3,
      particleColor: '#9E9E9E',
      surfaceChangeRate: 0.9
    },
    [ProcessType.ETCHING]: {
      power: 200,
      gasFlow: 100,
      speed: 6,
      gas: 'Ar/Cl₂',
      pressure: 1.2,
      temperature: 50,
      effectIntensity: 1.2,
      animationSpeed: 1.5,
      particleColor: '#FF6D00',
      surfaceChangeRate: 1.0
    },
    [ProcessType.COATING]: {
      power: 180,
      gasFlow: 80,
      speed: 5,
      gas: 'TiCl₄/N₂',
      pressure: 1.5,
      temperature: 60,
      effectIntensity: 1.1,
      animationSpeed: 1.0,
      particleColor: '#607D8B',
      surfaceChangeRate: 0.8
    }
  },
  [MaterialType.GLASS_CERAMIC]: {
    [ProcessType.ACTIVATION]: {
      power: 90,
      gasFlow: 45,
      speed: 10,
      gas: 'O₂/N₂',
      pressure: 0.4,
      temperature: 25,
      effectIntensity: 0.6,
      animationSpeed: 0.9,
      particleColor: '#2196F3',
      surfaceChangeRate: 0.5
    },
    [ProcessType.ETCHING]: {
      power: 130,
      gasFlow: 65,
      speed: 8,
      gas: 'CF₄/O₂',
      pressure: 0.7,
      temperature: 35,
      effectIntensity: 0.8,
      animationSpeed: 1.1,
      particleColor: '#FF9800',
      surfaceChangeRate: 0.7
    },
    [ProcessType.COATING]: {
      power: 110,
      gasFlow: 55,
      speed: 7,
      gas: 'SiO₂/Ar',
      pressure: 1.1,
      temperature: 45,
      effectIntensity: 0.7,
      animationSpeed: 0.9,
      particleColor: '#00E676',
      surfaceChangeRate: 0.6
    }
  }
};

// 快捷键配置
export const KEYBOARD_SHORTCUTS = {
  DEMO_START: 'KeyD',
  RESET: 'KeyR',
  HELP: 'KeyH',
  MATERIAL_1: 'Digit1',
  MATERIAL_2: 'Digit2',
  MATERIAL_3: 'Digit3',
  PROCESS_1: 'KeyQ',
  PROCESS_2: 'KeyW',
  PROCESS_3: 'KeyE',
  TOGGLE_PARAMS: 'KeyP',
  TOGGLE_COMPARISON: 'KeyC'
};

// 动画时长配置
export const ANIMATION_DURATIONS = {
  MATERIAL_CHANGE: 1000,
  PROCESS_START: 500,
  CAMERA_MOVE: 1500,
  PARTICLE_FADE: 300,
  UI_TRANSITION: 200
};


