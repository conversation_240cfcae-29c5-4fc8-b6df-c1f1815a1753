'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';

const Header = () => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const navigationItems = [
    {
      label: '网站首页',
      href: '/',
    },
    {
      label: '关于我们',
      href: '/about',
      children: [
        { label: '荣誉资质', href: '/honors' },
      ],
    },
    {
      label: '设备推荐',
      href: '/products',
      children: [
        { label: '小型真空等离子清洗机', href: '/products/small-vacuum' },
        { label: '大型真空等离子清洗机', href: '/products/large-vacuum' },
        { label: '大气等离子清洗机', href: '/products/atmospheric' },
      ],
    },
    {
      label: '新闻中心',
      href: '/news',
      children: [
        { label: '等离子清洗机百科', href: '/news/encyclopedia' },
        { label: '等离子清洗机应用', href: '/news/applications' },
      ],
    },
    {
      label: '3D演示',
      href: '/demo',
    },
    {
      label: '联系我们',
      href: '/contact-us',
    },
  ];

  return (
    <header className={`bg-white shadow-md transition-all duration-500 fixed top-0 left-0 right-0 z-50 ${isVisible ? 'header-visible' : 'header-hidden'}`}>
      {/* Logo and Navigation */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center flex-shrink-0">
            <div className="logo-container w-44 h-10 bg-gradient-to-r from-blue-600 to-blue-700 flex items-center justify-center text-white font-bold text-base rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
              等离子清洗专家
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden lg:flex items-center justify-center flex-1">
            <div className="flex items-center space-x-1">
              {navigationItems.map((item, index) => (
                <div
                  key={item.label}
                  className="relative group nav-item"
                  onMouseEnter={() => setActiveDropdown(item.label)}
                  onMouseLeave={() => setActiveDropdown(null)}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <Link
                    href={item.href}
                    className="nav-link text-gray-700 hover:text-blue-600 font-medium py-3 px-4 transition-all duration-300 relative overflow-hidden rounded-lg whitespace-nowrap block text-center"
                  >
                    <span className="relative z-10">{item.label}</span>
                    <div className="nav-link-bg absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 transform scale-x-0 origin-left transition-transform duration-300"></div>
                  </Link>

                {/* Clean & Elegant Dropdown Menu */}
                {item.children && (
                  <div
                    className={`dropdown-menu absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-64 bg-white border border-gray-200 shadow-lg rounded-lg z-[9999] overflow-hidden transition-all duration-200 ease-out ${
                      activeDropdown === item.label
                        ? 'opacity-100 visible translate-y-0'
                        : 'opacity-0 invisible -translate-y-2'
                    }`}
                    style={{
                      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
                    }}
                  >
                    {/* Simple Arrow */}
                    <div className="absolute -top-1 left-1/2 transform -translate-x-1/2">
                      <div className="w-2 h-2 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
                    </div>

                    <div className="py-2">
                      {item.children.map((child, childIndex) => (
                        <Link
                          key={child.label}
                          href={child.href}
                          className={`dropdown-item block px-4 py-3 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-all duration-150 relative group ${
                            activeDropdown === item.label ? 'animate-fadeIn' : ''
                          }`}
                          style={{
                            animationDelay: `${childIndex * 0.05}s`,
                            animationFillMode: 'both'
                          }}
                        >
                          <span className="flex items-center justify-between">
                            <span className="flex items-center">
                              <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-150"></span>
                              <span className="font-medium">{child.label}</span>
                            </span>
                            <svg className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-150 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </span>
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
                </div>
              ))}
            </div>
          </nav>

          {/* Mobile Menu Button */}
          <button className="lg:hidden p-2 text-gray-700 hover:text-blue-600 transition-colors duration-300 flex-shrink-0">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
