import Link from 'next/link';

const Footer = () => {
  return (
    <footer className="bg-gray-800 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Personal Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">等离子清洗技术服务</h3>
            <div className="space-y-2 text-sm text-gray-300">
              <p>技术顾问：曾先生</p>
              <p>手机：18954901489</p>
              <p>微信：wxid_gz1xribynia322</p>
              <p>邮箱：<EMAIL></p>
              <p>服务区域：南京市江宁区</p>
              <p className="text-blue-400 font-medium">咨询热线：18954901489</p>
              <p className="text-xs text-gray-400 mt-2">
                （专业等离子清洗技术咨询与设备推荐服务）
              </p>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">快速链接</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <Link href="/about" className="text-gray-300 hover:text-white transition-colors">
                关于我们
              </Link>
              <Link href="/products" className="text-gray-300 hover:text-white transition-colors">
                设备推荐
              </Link>
              <Link href="/news" className="text-gray-300 hover:text-white transition-colors">
                新闻中心
              </Link>
              <Link href="/contact-us" className="text-gray-300 hover:text-white transition-colors">
                联系我们
              </Link>
              <Link href="/honors" className="text-gray-300 hover:text-white transition-colors">
                荣誉资质
              </Link>
            </div>
          </div>

          {/* Product Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4">推荐设备</h3>
            <div className="space-y-2 text-sm">
              <Link href="/products/small-vacuum" className="block text-gray-300 hover:text-white transition-colors">
                小型真空等离子清洗机
              </Link>
              <Link href="/products/large-vacuum" className="block text-gray-300 hover:text-white transition-colors">
                大型真空等离子清洗机
              </Link>
              <Link href="/products/atmospheric" className="block text-gray-300 hover:text-white transition-colors">
                大气等离子清洗机
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-8 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-400">
            <div>
              <p>Copyright © 2024 等离子清洗技术服务 版权所有</p>
              <p className="text-xs mt-1">
                本站仅提供技术咨询与设备推荐服务，不涉及设备生产制造
              </p>
            </div>
            <div className="mt-4 md:mt-0">
              <Link 
                href="#top" 
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                TOP
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
