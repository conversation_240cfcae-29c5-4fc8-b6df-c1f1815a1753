'use client';

import { useState } from 'react';
import Image from 'next/image';

export default function WeChatButton() {
  const [showQRCode, setShowQRCode] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowQRCode(true)}
        className="inline-flex items-center gap-3 bg-white text-blue-600 px-8 py-4 rounded-full text-lg font-medium hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-lg"
      >
        微信联系
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      </button>

      {/* 微信二维码弹窗 */}
      {showQRCode && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl p-8 max-w-sm w-full mx-4 transform transition-all duration-300 scale-100">
            <div className="text-center">
              <h3 className="text-xl font-bold text-gray-800 mb-4">微信联系我们</h3>
              <div className="bg-gray-50 rounded-xl p-4 mb-4">
                <Image
                  src="/ewm.png"
                  alt="微信二维码"
                  width={192}
                  height={192}
                  className="mx-auto object-contain"
                />
              </div>
              <div className="space-y-2 text-sm text-gray-600 mb-6">
                <p><span className="font-medium">微信号：</span>wxid_gz1xribynia322</p>
                <p><span className="font-medium">手机号：</span>18954901489</p>
                <p className="text-blue-600">扫码或添加微信号联系我们</p>
              </div>
              <button
                onClick={() => setShowQRCode(false)}
                className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
