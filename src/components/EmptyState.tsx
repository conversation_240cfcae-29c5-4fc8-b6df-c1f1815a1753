import React from 'react';
import { Empty, Button } from 'antd';
import { PlusOutlined, FileTextOutlined, InboxOutlined } from '@ant-design/icons';

interface EmptyStateProps {
  type?: 'products' | 'news' | 'general';
  title?: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  type = 'general',
  title,
  description,
  actionText,
  onAction
}) => {
  const getIcon = () => {
    switch (type) {
      case 'products':
        return <InboxOutlined className="empty-state-icon" />;
      case 'news':
        return <FileTextOutlined className="empty-state-icon" />;
      default:
        return <InboxOutlined className="empty-state-icon" />;
    }
  };

  const getDefaultTitle = () => {
    switch (type) {
      case 'products':
        return '暂无产品数据';
      case 'news':
        return '暂无新闻数据';
      default:
        return '暂无数据';
    }
  };

  const getDefaultDescription = () => {
    switch (type) {
      case 'products':
        return '还没有添加任何产品，点击下方按钮开始添加';
      case 'news':
        return '还没有发布任何新闻，点击下方按钮开始发布';
      default:
        return '暂时没有相关数据';
    }
  };

  const getDefaultActionText = () => {
    switch (type) {
      case 'products':
        return '添加产品';
      case 'news':
        return '发布新闻';
      default:
        return '添加数据';
    }
  };

  return (
    <div className="empty-state">
      <Empty
        image={getIcon()}
        imageStyle={{
          height: 80,
          marginBottom: 16
        }}
        description={
          <div>
            <div className="empty-state-text">
              {title || getDefaultTitle()}
            </div>
            <div className="empty-state-description">
              {description || getDefaultDescription()}
            </div>
          </div>
        }
      >
        {onAction && (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={onAction}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: 6,
              boxShadow: '0 4px 15px rgba(102, 126, 234, 0.3)',
              minHeight: 40,
              padding: '0 24px'
            }}
          >
            {actionText || getDefaultActionText()}
          </Button>
        )}
      </Empty>
    </div>
  );
};

export default EmptyState;
