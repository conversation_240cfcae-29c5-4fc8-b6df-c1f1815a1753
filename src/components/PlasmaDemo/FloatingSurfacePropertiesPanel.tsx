import React from 'react';
import { Typography, Progress } from 'antd';
import { usePlasmaStore, selectCurrentMaterial, selectContactAngleData } from '@/stores/plasmaStore';

const { Text } = Typography;

const FloatingSurfacePropertiesPanel: React.FC = () => {
  const currentMaterial = usePlasmaStore(selectCurrentMaterial);
  const contactAngleData = usePlasmaStore(selectContactAngleData);

  if (!currentMaterial || !contactAngleData) {
    return null; // Don't show if no material selected
  }

  const improvement = ((contactAngleData.before - contactAngleData.after) / contactAngleData.before * 100);

  return (
    <div className="floating-surface-properties-panel">
      {/* 标题 */}
      <div className="panel-header">
        <Text strong style={{ fontSize: '12px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
          表面性质变化
        </Text>
      </div>

      {/* 接触角变化 */}
      <div className="property-item">
        <div className="property-header">
          <Text style={{ fontSize: '10px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            接触角变化
          </Text>
        </div>
        <div className="property-values">
          <Text style={{ fontSize: '11px', color: '#3498db', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            {contactAngleData.before}°
          </Text>
          <Text style={{ fontSize: '10px', color: '#d0d0d0', margin: '0 4px' }}>→</Text>
          <Text style={{ fontSize: '11px', color: '#2ecc71', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            {contactAngleData.after}°
          </Text>
        </div>
        <Progress
          percent={improvement}
          strokeColor={{
            '0%': '#ff7875',
            '50%': '#ffa940',
            '100%': '#73d13d'
          }}
          size="small"
          showInfo={false}
          strokeWidth={3}
        />
      </div>

      {/* 表面能变化 */}
      <div className="property-item">
        <div className="property-header">
          <Text style={{ fontSize: '10px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            表面能变化
          </Text>
        </div>
        <div className="property-values">
          <Text style={{ fontSize: '11px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            32 → 68 mN/m
          </Text>
          <Text style={{ fontSize: '10px', color: '#52c41a', marginLeft: '6px', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            (+36.0)
          </Text>
        </div>
      </div>

      {/* 表面粗糙度 */}
      <div className="property-item">
        <div className="property-header">
          <Text style={{ fontSize: '10px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            表面粗糙度
          </Text>
        </div>
        <div className="property-values">
          <Text style={{ fontSize: '11px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            {currentMaterial?.roughness || 0.1} → {((currentMaterial?.roughness || 0.1) * 3).toFixed(1)} μm
          </Text>
          <Text style={{ fontSize: '10px', color: '#faad14', marginLeft: '6px', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            (+{(((currentMaterial?.roughness || 0.1) * 3) - (currentMaterial?.roughness || 0.1)).toFixed(1)})
          </Text>
        </div>
      </div>

      {/* 润湿性改善 */}
      <div className="improvement-summary">
        <Text style={{ fontSize: '10px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
          润湿性改善:
        </Text>
        <Text strong style={{ fontSize: '12px', marginLeft: '6px', color: '#52c41a', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
          {improvement.toFixed(1)}%
        </Text>
      </div>

      <style jsx>{`
        .floating-surface-properties-panel {
          position: absolute;
          bottom: 20px;
          left: 20px;
          background: rgba(0, 0, 0, 0.25);
          backdrop-filter: blur(6px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          padding: 4px 6px;
          box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
          z-index: 40;
          min-width: 140px;
          max-width: 160px;
          pointer-events: auto;
          transition: all 0.3s ease;
        }

        .floating-surface-properties-panel:hover {
          background: rgba(0, 0, 0, 0.5);
          border-color: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
          min-width: 180px;
          max-width: 220px;
        }

        .panel-header {
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .property-item {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 6px;
          padding: 8px;
          margin-bottom: 8px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.2s ease;
        }

        .property-item:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
        }

        .property-header {
          margin-bottom: 4px;
        }

        .property-values {
          display: flex;
          align-items: center;
          margin-bottom: 6px;
        }

        .improvement-summary {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 8px;
          background: rgba(82, 196, 26, 0.1);
          border-radius: 6px;
          border: 1px solid rgba(82, 196, 26, 0.2);
          margin-top: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .floating-surface-properties-panel {
            bottom: 12px;
            left: 12px;
            min-width: 180px;
            max-width: 200px;
            padding: 10px;
          }
        }

        @media (max-width: 480px) {
          .floating-surface-properties-panel {
            bottom: 8px;
            left: 8px;
            min-width: 160px;
            max-width: 180px;
            padding: 8px;
          }
          
          .property-item {
            padding: 6px;
            margin-bottom: 6px;
          }
        }
      `}</style>
    </div>
  );
};

export default FloatingSurfacePropertiesPanel;
