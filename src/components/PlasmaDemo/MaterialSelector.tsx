import React from 'react';
import { Card, Space, Typography, Tag, message, Spin } from 'antd';
import { MaterialType } from '@/types/plasma';
import { usePlasmaStore, selectCurrentMaterial, selectContactAngleData } from '@/stores/plasmaStore';
import { useMaterials } from '@/hooks/useDemoData';

const { Text, Paragraph } = Typography;

// Helper function to get applications for each material
const getApplicationsForMaterial = (materialId?: MaterialType): string[] => {
  switch (materialId) {
    case MaterialType.POLYMER:
      return ['汽车零件', '电子外壳', '包装材料', '医疗器械'];
    case MaterialType.METAL:
      return ['航空航天', '医疗器械', '精密仪器', '电子元件'];
    case MaterialType.GLASS_CERAMIC:
      return ['光学器件', '显示屏', '实验器皿', '电子基板'];
    default:
      return [];
  }
};

interface MaterialSelectorProps {
  selectedMaterial: MaterialType;
  onMaterialSelect: (material: MaterialType) => void;
  disabled?: boolean;
}

const MaterialSelector: React.FC<MaterialSelectorProps> = ({
  selectedMaterial,
  onMaterialSelect,
  disabled = false
}) => {
  // 从API获取材料数据
  const { materials: apiMaterials, isLoading, error } = useMaterials();

  const { materials, setSelectedMaterial } = usePlasmaStore();
  const currentMaterial = usePlasmaStore(selectCurrentMaterial);
  const contactAngleData = usePlasmaStore(selectContactAngleData);
  // const isProcessRunning = usePlasmaStore(selectIsProcessRunning); // 预留用于未来功能

  // 使用API数据或fallback到store数据
  const materialOptions = apiMaterials?.length ? apiMaterials : (materials || []);

  const handleMaterialSelect = (materialId: MaterialType) => {
    // 直接切换材料，无需确认弹窗
    setSelectedMaterial(materialId, true);
    onMaterialSelect(materialId);
    const selectedMat = materialOptions.find(m => m.id === materialId);
    message.success(`已切换到 ${selectedMat?.displayName}`);
  };

  // 移除确认弹窗相关处理函数

  // 加载状态
  if (isLoading) {
    return (
      <div className="material-selector" style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        padding: '20px',
        gap: '12px'
      }}>
        <Spin />
        <Text style={{ color: '#666', fontSize: '12px' }}>正在加载材料数据...</Text>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="material-selector" style={{ textAlign: 'center', padding: '20px' }}>
        <Text type="danger">加载材料数据失败: {error}</Text>
      </div>
    );
  }

  return (
    <div className="material-selector">
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {materialOptions.map((material) => (
          <Card
            key={material.id}
            size="small"
            className={`material-card ${selectedMaterial === material.id ? 'selected' : ''}`}
            style={{
              cursor: disabled ? 'not-allowed' : 'pointer',
              border: selectedMaterial === material.id ? '2px solid #9C27B0' : '1px solid rgba(255, 255, 255, 0.2)',
              backgroundColor: selectedMaterial === material.id ? 'rgba(156, 39, 176, 0.15)' : 'rgba(255, 255, 255, 0.05)',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              transform: selectedMaterial === material.id ? 'scale(1.02)' : 'scale(1)',
              boxShadow: selectedMaterial === material.id ? '0 4px 12px rgba(156, 39, 176, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)'
            }}
            onClick={() => !disabled && handleMaterialSelect(material.id)}
            hoverable={!disabled}
          >
            <div className="material-content">
              <div className="material-header">
                <div className="material-name">
                  <Text strong style={{
                    fontSize: '16px',
                    color: '#ffffff',
                    textShadow: '0 2px 4px rgba(0,0,0,0.8)',
                    fontWeight: '600'
                  }}>
                    {material.displayName}
                  </Text>
                  <div
                    className="material-color-indicator"
                    style={{
                      width: '14px',
                      height: '14px',
                      backgroundColor: material.color,
                      borderRadius: '50%',
                      marginLeft: '8px',
                      border: '2px solid rgba(255,255,255,0.3)',
                      boxShadow: '0 2px 4px rgba(0,0,0,0.3)'
                    }}
                  />
                </div>
                {selectedMaterial === material.id && (
                  <Tag color="purple" style={{ fontSize: '12px' }}>当前选择</Tag>
                )}
              </div>

              <Paragraph
                style={{
                  margin: '6px 0 8px 0',
                  fontSize: '11px',
                  color: '#d0d0d0',
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)',
                  lineHeight: '1.3',
                  background: 'rgba(0,0,0,0.2)',
                  padding: '3px 6px',
                  borderRadius: '3px'
                }}
              >
                {material.description}
              </Paragraph>

              {selectedMaterial === material.id && currentMaterial && (
                <div className="material-properties">
                  <div className="property-grid">
                    <div className="property-item">
                      <Text style={{ fontSize: '11px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>粗糙度:</Text>
                      <Text style={{ fontSize: '11px', marginLeft: '4px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)', fontWeight: '600' }}>
                        {material.roughness}
                      </Text>
                    </div>
                    <div className="property-item">
                      <Text style={{ fontSize: '11px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>金属度:</Text>
                      <Text style={{ fontSize: '11px', marginLeft: '4px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)', fontWeight: '600' }}>
                        {material.metalness}
                      </Text>
                    </div>
                  </div>

                  <div className="surface-properties">
                    <Text strong style={{ fontSize: '12px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>表面性质变化:</Text>
                    <div className="property-changes">
                      {contactAngleData && (
                        <div className="change-item">
                          <Text style={{ fontSize: '11px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>接触角:</Text>
                          <Text style={{ fontSize: '11px', marginLeft: '4px', color: '#4CAF50', textShadow: '0 1px 2px rgba(0,0,0,0.8)', fontWeight: '600' }}>
                            {contactAngleData.before}° → {contactAngleData.after}°
                          </Text>
                        </div>
                      )}
                      <div className="change-item">
                        <Text style={{ fontSize: '11px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>表面能:</Text>
                        <Text style={{ fontSize: '11px', marginLeft: '4px', color: '#2196F3', textShadow: '0 1px 2px rgba(0,0,0,0.8)', fontWeight: '600' }}>
                          32 → 68 mN/m
                        </Text>
                      </div>
                      <div className="change-item">
                        <Text style={{ fontSize: '11px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>粗糙度:</Text>
                        <Text style={{ fontSize: '11px', marginLeft: '4px', color: '#FF9800', textShadow: '0 1px 2px rgba(0,0,0,0.8)', fontWeight: '600' }}>
                          {currentMaterial?.roughness || 0.1} → {(currentMaterial?.roughness || 0.1) * 3} μm
                        </Text>
                      </div>
                    </div>
                  </div>

                  <div className="applications">
                    <Text strong style={{ fontSize: '12px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>应用领域:</Text>
                    <div className="application-tags">
                      {getApplicationsForMaterial(currentMaterial?.id).map((app, index) => (
                        <Tag
                          key={index}
                          color="purple"
                          style={{
                            fontSize: '10px',
                            margin: '2px',
                            background: 'rgba(156, 39, 176, 0.8)',
                            border: '1px solid rgba(156, 39, 176, 1)',
                            color: '#ffffff',
                            textShadow: '0 1px 2px rgba(0,0,0,0.8)'
                          }}
                        >
                          {app}
                        </Tag>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Card>
        ))}
      </Space>

      <style jsx>{`
        .material-selector {
          width: 100%;
          max-height: 500px;
          overflow-y: auto;
          padding-right: 4px;
          scrollbar-width: thin;
          scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }

        .material-selector::-webkit-scrollbar {
          width: 6px;
        }

        .material-selector::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.05);
          border-radius: 3px;
        }

        .material-selector::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
        }

        .material-selector::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.3);
        }

        .material-card {
          transition: all 0.3s ease;
          margin-bottom: 8px;
          background: rgba(255, 255, 255, 0.05) !important;
          border: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .material-card:hover {
          box-shadow: 0 4px 12px rgba(156, 39, 176, 0.2);
          transform: translateY(-1px);
          background: rgba(255, 255, 255, 0.08) !important;
        }

        .material-content {
          padding: 2px;
        }

        .material-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;
        }

        .material-name {
          display: flex;
          align-items: center;
        }

        .material-properties {
          margin-top: 8px;
          padding-top: 6px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          max-height: 280px;
          overflow-y: auto;
        }

        .property-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 6px;
          margin: 6px 0;
        }

        .property-item {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
        }

        .surface-properties {
          margin: 12px 0;
        }

        .property-changes {
          margin-top: 6px;
        }

        .change-item {
          display: flex;
          align-items: center;
          margin: 4px 0;
          flex-wrap: wrap;
        }

        .applications {
          margin-top: 12px;
        }

        .application-tags {
          margin-top: 6px;
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }

        @media (max-width: 768px) {
          .material-selector {
            max-height: 400px;
          }

          .property-grid {
            grid-template-columns: 1fr;
            gap: 4px;
          }

          .material-properties {
            max-height: 200px;
          }
        }

        /* 材料卡片悬停效果 */
        .material-card:hover:not(.selected) {
          transform: scale(1.01) !important;
          border-color: rgba(156, 39, 176, 0.5) !important;
          box-shadow: 0 3px 10px rgba(156, 39, 176, 0.2) !important;
        }

        /* 材料切换动画 */
        .material-card.selected {
          animation: materialSelected 0.4s ease-out;
        }

        @keyframes materialSelected {
          0% {
            transform: scale(1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
          50% {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(156, 39, 176, 0.4);
          }
          100% {
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
          }
        }

        /* 材料属性变化动画 */
        .material-properties {
          animation: fadeInSlide 0.3s ease-out;
        }

        @keyframes fadeInSlide {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>

      {/* 移除材料切换确认对话框 */}
    </div>
  );
};

export default MaterialSelector;
