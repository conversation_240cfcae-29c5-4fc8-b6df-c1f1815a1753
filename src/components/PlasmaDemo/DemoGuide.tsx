import React from 'react';
import { Modal, Typography, Space, Button, Steps, Card, Tag } from 'antd';
import { PlayCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';

const { Paragraph, Text } = Typography;
const { Step } = Steps;

interface DemoGuideProps {
  open: boolean;
  onClose: () => void;
  onStartDemo: () => void;
}

const DemoGuide: React.FC<DemoGuideProps> = ({ open, onClose, onStartDemo }) => {
  const handleStartDemo = () => {
    onClose();
    onStartDemo();
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <QuestionCircleOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
          等离子清洗技术演示指南
        </div>
      }
      open={open}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="close" onClick={onClose}>
          稍后查看
        </Button>,
        <Button key="demo" type="primary" icon={<PlayCircleOutlined />} onClick={handleStartDemo}>
          开始演示
        </Button>
      ]}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 技术介绍 */}
        <Card size="small" title="技术概述">
          <Paragraph style={{ fontSize: '14px' }}>
            等离子清洗技术是一种先进的表面处理工艺，通过等离子体与材料表面的相互作用，
            实现表面清洁、活化、刻蚀和改性。本演示将展示不同材料在等离子处理前后的性能变化。
          </Paragraph>
        </Card>

        {/* 操作步骤 */}
        <Card size="small" title="操作步骤">
          <Steps direction="vertical" size="small">
            <Step
              title="选择材料"
              description="从聚合物、金属、玻璃陶瓷中选择要处理的材料类型"
              icon={<div style={{ backgroundColor: '#1890ff', color: 'white', borderRadius: '50%', width: '20px', height: '20px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>1</div>}
            />
            <Step
              title="选择工艺"
              description="根据处理需求选择表面活化、刻蚀或涂层工艺"
              icon={<div style={{ backgroundColor: '#52c41a', color: 'white', borderRadius: '50%', width: '20px', height: '20px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>2</div>}
            />
            <Step
              title="开始处理"
              description="点击开始按钮，观察3D场景中的等离子处理过程"
              icon={<div style={{ backgroundColor: '#faad14', color: 'white', borderRadius: '50%', width: '20px', height: '20px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>3</div>}
            />
            <Step
              title="查看结果"
              description="观察水滴角测试对比，了解表面性质的改善效果"
              icon={<div style={{ backgroundColor: '#f5222d', color: 'white', borderRadius: '50%', width: '20px', height: '20px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px' }}>4</div>}
            />
          </Steps>
        </Card>

        {/* 快捷键说明 */}
        <Card size="small" title="快捷键操作">
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
            <div className="shortcut-item">
              <Tag color="blue">D</Tag>
              <Text style={{ fontSize: '12px' }}>开始自动演示</Text>
            </div>
            <div className="shortcut-item">
              <Tag color="green">R</Tag>
              <Text style={{ fontSize: '12px' }}>重置演示</Text>
            </div>
            <div className="shortcut-item">
              <Tag color="orange">H</Tag>
              <Text style={{ fontSize: '12px' }}>显示帮助</Text>
            </div>
            <div className="shortcut-item">
              <Tag color="purple">1/2/3</Tag>
              <Text style={{ fontSize: '12px' }}>快速选择材料</Text>
            </div>
            <div className="shortcut-item">
              <Tag color="cyan">P</Tag>
              <Text style={{ fontSize: '12px' }}>切换参数显示</Text>
            </div>
            <div className="shortcut-item">
              <Tag color="magenta">C</Tag>
              <Text style={{ fontSize: '12px' }}>切换对比显示</Text>
            </div>
          </div>
        </Card>

        {/* 3D场景交互 */}
        <Card size="small" title="3D场景交互">
          <Space direction="vertical" size="small">
            <div className="interaction-item">
              <Text strong style={{ fontSize: '12px' }}>🖱️ 鼠标拖拽:</Text>
              <Text style={{ fontSize: '11px', marginLeft: '8px' }}>旋转视角，观察不同角度的处理效果</Text>
            </div>
            <div className="interaction-item">
              <Text strong style={{ fontSize: '12px' }}>🔍 视角控制:</Text>
              <Text style={{ fontSize: '11px', marginLeft: '8px' }}>自动调整最佳观察角度</Text>
            </div>
            <div className="interaction-item">
              <Text strong style={{ fontSize: '12px' }}>⚡ 实时效果:</Text>
              <Text style={{ fontSize: '11px', marginLeft: '8px' }}>观察等离子光束和粒子效果</Text>
            </div>
          </Space>
        </Card>

        {/* 技术特点 */}
        <Card size="small" title="技术特点">
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
            <div className="feature-item">
              <Text strong style={{ fontSize: '12px', color: '#1890ff' }}>🎯 精确控制</Text>
              <Text style={{ fontSize: '11px', display: 'block', marginTop: '4px' }}>
                精确控制处理参数，实现可重复的表面改性效果
              </Text>
            </div>
            <div className="feature-item">
              <Text strong style={{ fontSize: '12px', color: '#52c41a' }}>🌱 环保清洁</Text>
              <Text style={{ fontSize: '11px', display: 'block', marginTop: '4px' }}>
                无需化学溶剂，环保无污染的干式清洗工艺
              </Text>
            </div>
            <div className="feature-item">
              <Text strong style={{ fontSize: '12px', color: '#faad14' }}>⚡ 高效快速</Text>
              <Text style={{ fontSize: '11px', display: 'block', marginTop: '4px' }}>
                处理时间短，效率高，适合大批量生产
              </Text>
            </div>
            <div className="feature-item">
              <Text strong style={{ fontSize: '12px', color: '#f5222d' }}>🔬 多材料适用</Text>
              <Text style={{ fontSize: '11px', display: 'block', marginTop: '4px' }}>
                适用于多种材料，处理效果显著且稳定
              </Text>
            </div>
          </div>
        </Card>

        {/* 应用领域 */}
        <Card size="small" title="应用领域">
          <Space wrap>
            <Tag color="blue">汽车工业</Tag>
            <Tag color="green">电子制造</Tag>
            <Tag color="orange">医疗器械</Tag>
            <Tag color="purple">航空航天</Tag>
            <Tag color="cyan">包装印刷</Tag>
            <Tag color="magenta">光学器件</Tag>
            <Tag color="red">新能源</Tag>
            <Tag color="volcano">科研实验</Tag>
          </Space>
        </Card>

        {/* 注意事项 */}
        <Card size="small" title="注意事项" style={{ borderColor: '#faad14' }}>
          <Space direction="vertical" size="small">
            <Text type="warning" style={{ fontSize: '11px' }}>
              ⚠️ 本演示为技术展示，实际设备操作需要专业培训
            </Text>
            <Text type="warning" style={{ fontSize: '11px' }}>
              ⚠️ 不同材料和工艺参数会影响最终处理效果
            </Text>
            <Text type="warning" style={{ fontSize: '11px' }}>
              ⚠️ 实际应用前请进行工艺验证和参数优化
            </Text>
          </Space>
        </Card>
      </Space>

      <style jsx>{`
        .shortcut-item {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        
        .interaction-item {
          padding: 6px 0;
          border-bottom: 1px solid #f0f0f0;
        }
        
        .interaction-item:last-child {
          border-bottom: none;
        }
        
        .feature-item {
          padding: 8px;
          background: #fafafa;
          border-radius: 4px;
          border-left: 3px solid #1890ff;
        }
      `}</style>
    </Modal>
  );
};

export default DemoGuide;
