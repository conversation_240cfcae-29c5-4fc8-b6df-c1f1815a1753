'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, Button, Space, Typography, Divider, FloatButton, message, Spin, Alert } from 'antd';
import {
  ReloadOutlined,
  EyeOutlined,
  // EyeInvisibleOutlined, // 预留用于未来功能
  QuestionCircleOutlined,
  SettingOutlined,
  BarChartOutlined,
  FullscreenOutlined,
  // StopOutlined // 预留用于未来功能
} from '@ant-design/icons';
import { usePlasmaStore, selectIsProcessRunning, selectCurrentProcess } from '@/stores/plasmaStore';
import { MaterialType, ProcessType } from '@/types/plasma';
import { useAllDemoData, useRetry } from '@/hooks/useDemoData';
import ThreeScene from './ThreeScene';
import MaterialSelector from './MaterialSelector';
import ProcessControls from './ProcessControls';
import ProcessProgressControls from './ProcessProgressControls';
// import ContactAngleComparison from './ContactAngleComparison'; // 预留用于未来功能
// import ParametersPanel from './ParametersPanel'; // 预留用于未来功能
import DemoGuide from './DemoGuide';
import FullscreenDemo from './FullscreenDemo';
import MaterialEffects from './MaterialEffects';
import FloatingParametersPanel from './FloatingParametersPanel';
import FloatingSurfacePropertiesPanel from './FloatingSurfacePropertiesPanel';
import FloatingPerformancePanel from './FloatingPerformancePanel';

const { Title } = Typography;

const PlasmaDemo: React.FC = () => {
  const [showGuide, setShowGuide] = useState(false);
  const [showParameters, setShowParameters] = useState(true);
  const [showComparison, setShowComparison] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // API数据获取
  const {
    // materials, processes, config, // 预留用于未来功能
    isLoading,
    error,
    refetch
  } = useAllDemoData();
  const { retry, canRetry } = useRetry(refetch);

  // Zustand store hooks
  const { scene, setSelectedMaterial, setSelectedProcess, startProcess, resetScene } = usePlasmaStore();
  const isProcessRunning = usePlasmaStore(selectIsProcessRunning);
  // const currentMaterial = usePlasmaStore(selectCurrentMaterial); // 预留用于未来功能
  const currentProcess = usePlasmaStore(selectCurrentProcess);

  // 自动演示
  const startAutoDemo = useCallback(() => {
    message.info('开始自动演示');

    // 选择第一个工艺并开始
    const firstProcess = ProcessType.ACTIVATION;
    setSelectedProcess(firstProcess);
    setTimeout(() => {
      startProcess();
    }, 1000);
  }, [setSelectedProcess, startProcess]);

  // 重置演示
  const handleReset = useCallback(() => {
    resetScene();
    message.info('演示已重置');
  }, [resetScene]);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return; // 忽略输入框中的按键
      }

      switch (event.code) {
        case 'KeyD':
          if (!isProcessRunning) {
            startAutoDemo();
          }
          break;
        case 'KeyR':
          handleReset();
          break;
        case 'KeyH':
          setShowGuide(true);
          break;
        case 'Digit1':
          setSelectedMaterial(MaterialType.POLYMER);
          break;
        case 'Digit2':
          setSelectedMaterial(MaterialType.METAL);
          break;
        case 'Digit3':
          setSelectedMaterial(MaterialType.GLASS_CERAMIC);
          break;
        case 'KeyP':
          setShowParameters(!showParameters);
          break;
        case 'KeyC':
          setShowComparison(!showComparison);
          break;
        case 'KeyF':
          event.preventDefault();
          toggleFullscreen();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isProcessRunning, showParameters, showComparison, setSelectedMaterial, handleReset, startAutoDemo]);



  // 处理材料选择 - 现在由MaterialSelector内部处理，这里只是保持接口兼容
  const handleMaterialSelect = (material: MaterialType) => {
    // MaterialSelector组件内部已经处理了切换逻辑，这里不需要额外操作
    console.log('Material selected:', material);
  };

  // 处理工艺开始
  const handleProcessStart = (process: ProcessType) => {
    setSelectedProcess(process);
    startProcess();
    message.success(`开始 ${currentProcess?.displayName} 处理`);
  };





  // 切换全屏
  const toggleFullscreen = () => {
    setIsFullscreen(true);
    // Emit custom event to notify parent component
    window.dispatchEvent(new CustomEvent('plasmaFullscreenChange', {
      detail: { isFullscreen: true }
    }));
  };

  // 退出全屏
  const exitFullscreen = () => {
    setIsFullscreen(false);
    // Emit custom event to notify parent component
    window.dispatchEvent(new CustomEvent('plasmaFullscreenChange', {
      detail: { isFullscreen: false }
    }));
  };

  // 加载状态
  if (isLoading) {
    return (
      <div className="plasma-demo-container" style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '400px',
        gap: '16px'
      }}>
        <Spin size="large" />
        <div style={{ color: '#666', fontSize: '14px' }}>正在加载演示数据...</div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="plasma-demo-container">
        <Alert
          message="数据加载失败"
          description={error}
          type="error"
          showIcon
          action={
            canRetry ? (
              <Button size="small" onClick={retry}>
                重试
              </Button>
            ) : (
              <Button size="small" onClick={refetch}>
                刷新
              </Button>
            )
          }
        />
      </div>
    );
  }

  // 如果是全屏模式，渲染全屏组件
  if (isFullscreen) {
    return <FullscreenDemo onExitFullscreen={exitFullscreen} />;
  }

  return (
    <div className="plasma-demo-container">
      {/* 主标题 */}
      <div className="demo-header">
        <Title level={2} style={{ margin: 0, color: '#ffffff' }}>
          等离子清洗技术3D演示
        </Title>
        <Space>
          <Button
            icon={<QuestionCircleOutlined />}
            onClick={() => setShowGuide(true)}
            size="small"
          >
            使用指南
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleReset}
            disabled={isProcessRunning}
            size="small"
          >
            重置
          </Button>
          <Button
            icon={<FullscreenOutlined />}
            onClick={toggleFullscreen}
            size="small"
          >
            全屏显示
          </Button>
        </Space>
      </div>

      <Divider style={{ margin: '16px 0' }} />

      {/* 主要内容区域 */}
      <div className="demo-content">
        {/* 简化的控制面板 - 只保留基本控制 */}
        <div className="control-panel">
          {/* 材料选择 */}
          <Card
            title="材料选择"
            size="small"
            className="control-card"
          >
            <MaterialSelector
              selectedMaterial={scene.selectedMaterial}
              onMaterialSelect={handleMaterialSelect}
              disabled={false}
            />
          </Card>

          {/* 工艺控制 */}
          <Card
            title="工艺控制"
            size="small"
            className="control-card"
          >
            <ProcessControls
              selectedProcess={scene.selectedProcess}
              onProcessStart={handleProcessStart}
              disabled={false}
              isRunning={isProcessRunning}
            />
          </Card>
        </div>

        {/* 中央3D场景 - 带浮动面板 */}
        <div className="scene-container">
          {/* 浮动进度控制面板 - 右上角 */}
          <ProcessProgressControls
            isRunning={isProcessRunning}
            onPause={() => message.info('已暂停处理')}
            onResume={() => message.success('已恢复处理')}
            onStop={() => message.info('已停止处理')}
            onRestart={() => message.success('重新开始处理')}
          />

          {/* 浮动实时参数监控面板 - 左上角 */}
          <FloatingParametersPanel />

          {/* 浮动表面性质变化面板 - 左下角 */}
          <FloatingSurfacePropertiesPanel />

          {/* 浮动性能改善数据面板 - 右下角 */}
          <FloatingPerformancePanel />

          <ThreeScene className="three-scene" />
        </div>

        {/* 简化的信息面板 */}
        <div className="info-panel">
          {/* 材料特定效果 */}
          <MaterialEffects className="info-card" />

          {/* 技术说明 */}
          <div className="info-card tech-info">
            <div className="card-title">技术说明</div>
            <div className="tech-content">
              <div className="tech-section">
                <h4 style={{
                  color: '#ffffff',
                  fontSize: '14px',
                  marginBottom: '8px',
                  textShadow: '0 2px 4px rgba(0,0,0,0.8)',
                  background: 'rgba(156, 39, 176, 0.2)',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  border: '1px solid rgba(156, 39, 176, 0.3)'
                }}>表面活化</h4>
                <p style={{
                  color: '#d0d0d0',
                  fontSize: '12px',
                  lineHeight: '1.5',
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)',
                  background: 'rgba(0,0,0,0.3)',
                  padding: '6px',
                  borderRadius: '4px'
                }}>通过等离子体处理提高材料表面能，改善润湿性和粘接性能。</p>
              </div>

              <div className="tech-section">
                <h4 style={{
                  color: '#ffffff',
                  fontSize: '14px',
                  marginBottom: '8px',
                  textShadow: '0 2px 4px rgba(0,0,0,0.8)',
                  background: 'rgba(255, 87, 34, 0.2)',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  border: '1px solid rgba(255, 87, 34, 0.3)'
                }}>刻蚀</h4>
                <p style={{
                  color: '#d0d0d0',
                  fontSize: '12px',
                  lineHeight: '1.5',
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)',
                  background: 'rgba(0,0,0,0.3)',
                  padding: '6px',
                  borderRadius: '4px'
                }}>去除表面污染物和氧化层，增加表面粗糙度，提高附着力。</p>
              </div>

              <div className="tech-section">
                <h4 style={{
                  color: '#ffffff',
                  fontSize: '14px',
                  marginBottom: '8px',
                  textShadow: '0 2px 4px rgba(0,0,0,0.8)',
                  background: 'rgba(0, 188, 212, 0.2)',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  border: '1px solid rgba(0, 188, 212, 0.3)'
                }}>镀膜</h4>
                <p style={{
                  color: '#d0d0d0',
                  fontSize: '12px',
                  lineHeight: '1.5',
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)',
                  background: 'rgba(0,0,0,0.3)',
                  padding: '6px',
                  borderRadius: '4px'
                }}>在材料表面沉积功能性薄膜，赋予特殊性能。</p>
              </div>
            </div>
          </div>

          {/* 应用领域 */}
          <div className="info-card applications">
            <div className="card-title">应用领域</div>
            <div className="applications-content">
              <div className="application-item">
                <span className="app-icon">🚗</span>
                <span className="app-text" style={{ color: '#ffffff' }}>汽车工业</span>
              </div>
              <div className="application-item">
                <span className="app-icon">📱</span>
                <span className="app-text" style={{ color: '#ffffff' }}>电子产品</span>
              </div>
              <div className="application-item">
                <span className="app-icon">🏥</span>
                <span className="app-text" style={{ color: '#ffffff' }}>医疗器械</span>
              </div>
              <div className="application-item">
                <span className="app-icon">✈️</span>
                <span className="app-text" style={{ color: '#ffffff' }}>航空航天</span>
              </div>
              <div className="application-item">
                <span className="app-icon">📦</span>
                <span className="app-text" style={{ color: '#ffffff' }}>包装材料</span>
              </div>
              <div className="application-item">
                <span className="app-icon">🔬</span>
                <span className="app-text" style={{ color: '#ffffff' }}>科研实验</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 浮动按钮 */}
      <FloatButton.Group
        trigger="hover"
        type="primary"
        style={{ right: 24 }}
        icon={<SettingOutlined />}
      >
        {!showParameters && (
          <FloatButton
            icon={<BarChartOutlined />}
            tooltip="显示参数监控"
            onClick={() => setShowParameters(true)}
          />
        )}
        {!showComparison && (
          <FloatButton
            icon={<EyeOutlined />}
            tooltip="显示性能对比"
            onClick={() => setShowComparison(true)}
          />
        )}
        <FloatButton
          icon={<QuestionCircleOutlined />}
          tooltip="使用指南"
          onClick={() => setShowGuide(true)}
        />
      </FloatButton.Group>

      {/* 使用指南弹窗 */}
      <DemoGuide
        open={showGuide}
        onClose={() => setShowGuide(false)}
        onStartDemo={startAutoDemo}
      />

      <style jsx>{`
        .plasma-demo-container {
          height: 100vh;
          background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
          color: #ffffff;
          overflow: hidden;
          padding: 24px;
          width: 100vw;
          max-width: none;
          margin: 0;
          box-sizing: border-box;
        }

        .demo-header {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 20px 24px;
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(10px);
          border-radius: 12px;
          margin-bottom: 24px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .three-scene {
          width: 100%;
          height: 100%;
          border-radius: 16px;
          overflow: hidden;
        }

        :global(.ant-card) {
          background: rgba(255, 255, 255, 0.08) !important;
          border: 1px solid rgba(255, 255, 255, 0.12) !important;
          border-radius: 12px !important;
          backdrop-filter: blur(10px) !important;
          transition: all 0.5s ease !important;
        }

        :global(.ant-card:hover) {
          background: rgba(255, 255, 255, 0.12) !important;
          transform: translateY(-2px) !important;
        }

        :global(.ant-card-head) {
          background: rgba(255, 255, 255, 0.12) !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
          border-radius: 12px 12px 0 0 !important;
          backdrop-filter: blur(15px) !important;
          padding: 12px 16px !important;
        }

        :global(.ant-card-head-title) {
          color: #ffffff !important;
          font-weight: 700 !important;
          font-size: 16px !important;
          text-rendering: optimizeLegibility !important;
          -webkit-font-smoothing: antialiased !important;
          -moz-osx-font-smoothing: grayscale !important;
          letter-spacing: 0.5px !important;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8), 0 0 12px rgba(156, 39, 176, 0.4) !important;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
          line-height: 1.4 !important;
          font-variant-ligatures: none !important;
          font-feature-settings: "kern" 1 !important;
          text-transform: none !important;
          word-spacing: normal !important;
          background: rgba(0, 0, 0, 0.3) !important;
          padding: 2px 8px !important;
          border-radius: 4px !important;
        }

        :global(.ant-btn) {
          background: rgba(255, 255, 255, 0.1) !important;
          border: 1px solid rgba(255, 255, 255, 0.2) !important;
          color: #ffffff !important;
          border-radius: 8px !important;
          transition: all 0.3s ease !important;
        }

        :global(.ant-btn:hover) {
          background: rgba(255, 255, 255, 0.2) !important;
          border-color: rgba(255, 255, 255, 0.3) !important;
          transform: translateY(-1px) !important;
        }

        :global(.ant-btn-primary) {
          background: linear-gradient(135deg, #9C27B0, #673AB7) !important;
          border: none !important;
          box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3) !important;
          font-weight: 600 !important;
        }

        :global(.ant-btn-primary:hover) {
          background: linear-gradient(135deg, #7B1FA2, #512DA8) !important;
          transform: translateY(-2px) !important;
          box-shadow: 0 6px 16px rgba(156, 39, 176, 0.4) !important;
        }

        :global(.ant-divider) {
          border-color: rgba(255, 255, 255, 0.1) !important;
        }

        :global(.ant-card-body) {
          background: transparent !important;
          padding: 12px 16px !important;
        }

        // 优化控制面板内的Card padding
        .control-panel :global(.ant-card-body) {
          padding: 8px 12px !important;
        }

        .info-panel :global(.ant-card-body) {
          padding: 8px 12px !important;
        }

        // 主要内容区域
        .demo-content {
          display: flex;
          height: calc(100vh - 200px);
          gap: 16px;
        }

        // 左侧控制面板 - 优化版
        .control-panel {
          width: 280px;
          display: flex;
          flex-direction: column;
          gap: 12px;
          overflow-y: auto;
          padding-right: 8px;
        }

        .control-card {
          background: rgba(255, 255, 255, 0.08) !important;
          border: 1px solid rgba(255, 255, 255, 0.12) !important;
          border-radius: 12px !important;
          backdrop-filter: blur(10px) !important;
          transition: all 0.5s ease !important;
        }

        // 中央3D场景
        .scene-container {
          flex: 1;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 16px;
          overflow: hidden;
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-height: 400px;
        }

        .scene-stop-button-container {
          position: absolute;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 10;
          animation: fadeInUp 0.3s ease-out;
        }

        .scene-stop-button {
          box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3) !important;
          border: none !important;
          font-weight: 600 !important;
          padding: 8px 24px !important;
          height: auto !important;
          border-radius: 8px !important;
          transition: all 0.3s ease !important;
        }

        .scene-stop-button:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 6px 16px rgba(244, 67, 54, 0.4) !important;
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateX(-50%) translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
          }
        }

        // 右侧信息面板 - 优化版
        .info-panel {
          width: 280px;
          display: flex;
          flex-direction: column;
          gap: 12px;
          overflow-y: auto;
          padding-left: 8px;
        }

        .info-card {
          background: rgba(255, 255, 255, 0.08) !important;
          border: 1px solid rgba(255, 255, 255, 0.12) !important;
          border-radius: 12px !important;
          backdrop-filter: blur(10px) !important;
          transition: all 0.5s ease !important;
          padding: 16px;
          margin-bottom: 16px;
        }

        .card-title {
          color: #ffffff;
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 12px;
          text-shadow: 0 2px 4px rgba(0,0,0,0.8);
          background: rgba(255,255,255,0.1);
          padding: 6px 12px;
          border-radius: 6px;
          border: 1px solid rgba(255,255,255,0.2);
        }

        // 技术说明
        .tech-info .tech-content .tech-section {
          margin-bottom: 16px;
        }

        .tech-info .tech-content .tech-section:last-child {
          margin-bottom: 0;
        }

        .tech-info .tech-content .tech-section h4 {
          color: #ffffff;
          font-size: 14px;
          font-weight: 600;
          margin: 0 0 6px 0;
        }

        .tech-info .tech-content .tech-section p {
          color: #b0b0b0;
          font-size: 12px;
          line-height: 1.4;
          margin: 0;
        }

        // 应用领域
        .applications .applications-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;
        }

        .applications .application-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          transition: all 0.3s ease;
        }

        .applications .application-item:hover {
          background: rgba(255, 255, 255, 0.1);
          transform: translateY(-2px);
        }

        .applications .app-icon {
          font-size: 16px;
        }

        .applications .app-text {
          font-size: 12px;
          color: #ffffff;
          font-weight: 500;
          text-shadow: 0 1px 2px rgba(0,0,0,0.8);
        }

        // 滚动条样式
        .control-panel::-webkit-scrollbar,
        .info-panel::-webkit-scrollbar {
          width: 6px;
        }

        .control-panel::-webkit-scrollbar-track,
        .info-panel::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }

        .control-panel::-webkit-scrollbar-thumb,
        .info-panel::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.3);
          border-radius: 3px;
        }

        .control-panel::-webkit-scrollbar-thumb:hover,
        .info-panel::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.5);
        }

        @media (max-width: 1400px) {
          .demo-content .control-panel {
            width: 260px;
          }

          .demo-content .info-panel {
            width: 260px;
          }
        }

        @media (max-width: 1200px) {
          .plasma-demo-container {
            padding: 16px;
          }

          .demo-content .control-panel {
            width: 240px;
          }

          .demo-content .info-panel {
            width: 240px;
          }

          :global(.ant-card-head-title) {
            font-size: 15px !important;
          }

          // 进一步减少小屏幕上的padding
          .control-panel :global(.ant-card-body),
          .info-panel :global(.ant-card-body) {
            padding: 6px 10px !important;
          }
        }

        @media (max-width: 768px) {
          .plasma-demo-container {
            padding: 12px;
          }

          .demo-header {
            flex-direction: column;
            gap: 12px;
            padding: 12px 16px;
          }

          .demo-content {
            flex-direction: column;
            height: auto;
            min-height: calc(100vh - 140px);
          }

          .demo-content .control-panel,
          .demo-content .info-panel {
            width: 100%;
            flex-direction: row;
            overflow-x: auto;
            gap: 8px;
            padding: 0 4px;
          }

          .demo-content .control-card,
          .demo-content .info-card {
            min-width: 280px;
            flex-shrink: 0;
          }

          // 移动端进一步优化padding
          .control-panel :global(.ant-card-body),
          .info-panel :global(.ant-card-body) {
            padding: 4px 8px !important;
          }

          .demo-content .scene-container {
            height: 400px;
            order: -1;
          }

          :global(.ant-card-head-title) {
            font-size: 14px !important;
            letter-spacing: 0.3px !important;
          }
        }

        @media (max-width: 480px) {
          .plasma-demo-container {
            padding: 8px;
          }

          .demo-header {
            padding: 8px 12px;
          }

          :global(.ant-card-head-title) {
            font-size: 13px !important;
            letter-spacing: 0.2px !important;
            font-weight: 800 !important;
          }

          :global(.ant-card-head) {
            padding: 10px 12px !important;
          }
        }

        /* 高DPI屏幕优化 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
          :global(.ant-card-head-title) {
            -webkit-font-smoothing: subpixel-antialiased !important;
            text-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.4), 0 0 6px rgba(156, 39, 176, 0.2) !important;
          }
        }

        /* 超高DPI屏幕优化 */
        @media (-webkit-min-device-pixel-ratio: 3), (min-resolution: 288dpi) {
          :global(.ant-card-head-title) {
            font-weight: 600 !important;
            letter-spacing: 0.3px !important;
            text-shadow: 0 0.25px 0.5px rgba(0, 0, 0, 0.3), 0 0 4px rgba(156, 39, 176, 0.15) !important;
          }
        }
      `}</style>
    </div>
  );
};

export default PlasmaDemo;
