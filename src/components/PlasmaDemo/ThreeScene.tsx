import React, { useRef, useEffect, useCallback } from 'react';
import * as THREE from 'three';
import { usePlasmaStore, selectIsProcessRunning, selectCurrentMaterial, selectCurrentProcess } from '@/stores/plasmaStore';
import { MATERIAL_SPECIFIC_PARAMETERS } from '@/constants/plasma';

// 性能优化：节流函数
const throttle = <T extends (...args: any[]) => any>(func: T, limit: number): T => {
  let inThrottle: boolean;
  return ((...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }) as T;
};

interface ThreeSceneProps {
  className?: string;
}

const ThreeScene: React.FC<ThreeSceneProps> = ({ className }) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const workpieceRef = useRef<THREE.Group | null>(null);
  const plasmaGunRef = useRef<THREE.Group | null>(null);
  const plasmaBeamRef = useRef<THREE.Group | null>(null);
  const particleSystemRef = useRef<THREE.Points | null>(null);
  const animationIdRef = useRef<number | null>(null);

  // 性能优化：使用ref代替state来避免不必要的重渲染
  const isMouseDownRef = useRef(false);
  const mousePosRef = useRef({ x: 0, y: 0 });
  const cameraRotationRef = useRef({ x: 0, y: 0 });
  const cameraUpdateNeededRef = useRef(false);
  const lastFrameTimeRef = useRef(0);
  const frameCountRef = useRef(0);
  const fpsRef = useRef(60);

  // Use ref for plasma intensity to avoid React re-renders
  const plasmaIntensityRef = useRef(0);
  const fadeAnimationRef = useRef<number | null>(null);

  // 缓存材料属性以避免重复计算
  const materialPropsCache = useRef<Record<string, any> | null>(null);
  const lastMaterialType = useRef<string>('');

  // 从Zustand获取状态
  const { scene } = usePlasmaStore();
  const isProcessRunning = usePlasmaStore(selectIsProcessRunning);
  const currentMaterial = usePlasmaStore(selectCurrentMaterial);
  const currentProcess = usePlasmaStore(selectCurrentProcess);

  // 获取当前材料和工艺的特定参数
  const getMaterialSpecificParams = useCallback(() => {
    if (!currentMaterial || !currentProcess) return null;
    return (MATERIAL_SPECIFIC_PARAMETERS as Record<string, Record<string, any>>)[currentMaterial.id]?.[currentProcess.id];
  }, [currentMaterial, currentProcess]);

  // 管理等离子效果强度 - 使用 ref 避免 React 重渲染
  useEffect(() => {
    if (isProcessRunning) {
      plasmaIntensityRef.current = 1.0;
      if (fadeAnimationRef.current) {
        cancelAnimationFrame(fadeAnimationRef.current);
      }
    } else {
      // 使用 requestAnimationFrame 实现渐隐效果，避免 React 重渲染
      const startFade = () => {
        if (plasmaIntensityRef.current > 0) {
          plasmaIntensityRef.current = Math.max(0, plasmaIntensityRef.current - 0.02);
          fadeAnimationRef.current = requestAnimationFrame(startFade);
        }
      };
      fadeAnimationRef.current = requestAnimationFrame(startFade);
    }

    return () => {
      if (fadeAnimationRef.current) {
        cancelAnimationFrame(fadeAnimationRef.current);
      }
    };
  }, [isProcessRunning]);

  // 获取材料颜色 (保留以备将来使用)
  // const getMaterialColor = () => {
  //   const currentMaterial = usePlasmaStore.getState().materials.find(
  //     m => m.id === scene.selectedMaterial
  //   );
  //   return currentMaterial ? currentMaterial.color : '#4CAF50';
  // };

  // 获取材料属性 - 增强版本，包含动画特性，添加缓存机制
  const getMaterialProperties = useCallback(() => {
    const materialType = scene.selectedMaterial;

    // 性能优化：如果材料类型没有变化，返回缓存的属性
    if (lastMaterialType.current === materialType && materialPropsCache.current) {
      return materialPropsCache.current;
    }

    const materialParams = getMaterialSpecificParams();

    let materialProps;
    switch (materialType) {
      case 'polymer':
        materialProps = {
          color: 0x4CAF50,
          metalness: 0.0,
          roughness: 0.8,
          clearcoat: 0.1,
          clearcoatRoughness: 0.9,
          transparent: false,
          opacity: 1.0,
          detailColor: 0x2E7D32,
          hasGlow: false,
          glowColor: 0x4CAF50,
          // 聚合物特有的动画特性
          surfaceActivationEffect: true,
          hydrophilicTransition: true,
          bondingImprovement: true,
          particleColor: materialParams?.particleColor || '#4CAF50',
          animationSpeed: materialParams?.animationSpeed || 1.0,
          effectIntensity: materialParams?.effectIntensity || 0.8
        };
        break;
      case 'metal':
        materialProps = {
          color: 0x9E9E9E,
          metalness: 0.9,
          roughness: 0.1,
          clearcoat: 0.0,
          clearcoatRoughness: 0.0,
          transparent: false,
          opacity: 1.0,
          detailColor: 0x616161,
          hasGlow: true,
          glowColor: 0xBBBBBB,
          // 金属特有的动画特性
          oxideRemoval: true,
          surfaceRoughening: true,
          conductivityImprovement: true,
          particleColor: materialParams?.particleColor || '#9E9E9E',
          animationSpeed: materialParams?.animationSpeed || 1.3,
          effectIntensity: materialParams?.effectIntensity || 1.0
        };
        break;
      case 'glass_ceramic':
        materialProps = {
          color: 0x2196F3,
          metalness: 0.0,
          roughness: 0.05,
          clearcoat: 1.0,
          clearcoatRoughness: 0.0,
          transparent: true,
          opacity: 0.9,
          detailColor: 0x1976D2,
          hasGlow: true,
          glowColor: 0x2196F3,
          // 玻璃陶瓷特有的动画特性
          surfaceCleaning: true,
          bondingEnhancement: true,
          opticalImprovement: true,
          particleColor: materialParams?.particleColor || '#2196F3',
          animationSpeed: materialParams?.animationSpeed || 0.9,
          effectIntensity: materialParams?.effectIntensity || 0.6
        };
        break;
      default:
        materialProps = {
          color: 0x4CAF50,
          metalness: 0.0,
          roughness: 0.5,
          clearcoat: 0.0,
          clearcoatRoughness: 0.5,
          transparent: false,
          opacity: 1.0,
          detailColor: 0x2E7D32,
          hasGlow: false,
          glowColor: 0x4CAF50,
          particleColor: '#4CAF50',
          animationSpeed: 1.0,
          effectIntensity: 0.5
        };
        break;
    }

    // 缓存材料属性
    materialPropsCache.current = materialProps;
    lastMaterialType.current = materialType;

    return materialProps;
  }, [scene.selectedMaterial, getMaterialSpecificParams]);

  // 创建工件模型
  const createWorkpiece = useCallback(() => {
    if (!sceneRef.current) return;

    const workpieceGroup = new THREE.Group();

    // 主体 - 圆柱形工件
    const geometry = new THREE.CylinderGeometry(1, 1, 0.3, 32);
    const materialProps = getMaterialProperties();

    const material = new THREE.MeshPhysicalMaterial({
      color: materialProps.color,
      metalness: materialProps.metalness,
      roughness: materialProps.roughness,
      clearcoat: materialProps.clearcoat,
      clearcoatRoughness: materialProps.clearcoatRoughness,
      envMapIntensity: 1.0,
      transparent: materialProps.transparent,
      opacity: materialProps.opacity
    });

    const workpiece = new THREE.Mesh(geometry, material);
    workpiece.castShadow = true;
    workpiece.receiveShadow = true;
    workpiece.userData = { materialType: scene.selectedMaterial };
    workpieceGroup.add(workpiece);

    // 添加表面细节和纹理
    const detailGeometry = new THREE.RingGeometry(0.8, 1.0, 16);
    const detailMaterial = new THREE.MeshPhysicalMaterial({
      color: materialProps.detailColor,
      metalness: materialProps.metalness * 0.5,
      roughness: materialProps.roughness + 0.2,
      transparent: true,
      opacity: 0.4
    });

    const detail = new THREE.Mesh(detailGeometry, detailMaterial);
    detail.position.y = 0.151;
    detail.rotation.x = -Math.PI / 2;
    workpieceGroup.add(detail);

    // 添加材料特有的视觉效果
    if (materialProps.hasGlow) {
      const glowGeometry = new THREE.CylinderGeometry(1.05, 1.05, 0.32, 32);
      const glowMaterial = new THREE.MeshBasicMaterial({
        color: materialProps.glowColor,
        transparent: true,
        opacity: 0.1,
        side: THREE.BackSide
      });
      const glow = new THREE.Mesh(glowGeometry, glowMaterial);
      workpieceGroup.add(glow);
    }

    workpieceGroup.position.set(0, 0, 0);
    sceneRef.current.add(workpieceGroup);
    workpieceRef.current = workpieceGroup;
  }, [scene.selectedMaterial, getMaterialProperties]);

  // 动画循环 - 性能优化版本
  const animate = useCallback(() => {
    if (!rendererRef.current || !sceneRef.current || !cameraRef.current) return;

    // 帧率监控
    const currentTime = performance.now();
    const deltaTime = currentTime - lastFrameTimeRef.current;

    // 限制最大帧率到60fps (16.67ms per frame)
    if (deltaTime < 16.67) {
      animationIdRef.current = requestAnimationFrame(animate);
      return;
    }

    lastFrameTimeRef.current = currentTime;
    frameCountRef.current++;

    // 每60帧计算一次FPS
    if (frameCountRef.current % 60 === 0) {
      fpsRef.current = Math.round(1000 / deltaTime);
    }

    // 更新相机位置 - 使用ref避免React重渲染
    if (cameraUpdateNeededRef.current) {
      const camera = cameraRef.current;
      const radius = 8;
      const rotation = cameraRotationRef.current;
      camera.position.x = Math.cos(rotation.y) * Math.cos(rotation.x) * radius;
      camera.position.y = Math.sin(rotation.x) * radius + 3;
      camera.position.z = Math.sin(rotation.y) * Math.cos(rotation.x) * radius;
      camera.lookAt(0, 0, 0);
      cameraUpdateNeededRef.current = false;
    }

    // 更新等离子效果
    if (plasmaBeamRef.current) {
      plasmaBeamRef.current.visible = plasmaIntensityRef.current > 0;
      if (plasmaBeamRef.current.visible) {
        plasmaBeamRef.current.children.forEach((child, index) => {
          if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshBasicMaterial) {
            child.material.opacity = plasmaIntensityRef.current * (index === 0 ? 0.8 : 0.9);
          }
        });
      }
    }

    // 更新粒子系统 - 基于材料特性，性能优化
    if (particleSystemRef.current && plasmaIntensityRef.current > 0) {
      particleSystemRef.current.visible = true;

      // 性能优化：缓存材料属性，避免每帧重复计算
      const materialProps = materialPropsCache.current || getMaterialProperties();
      const positions = particleSystemRef.current.geometry.attributes.position.array as Float32Array;
      const velocities = particleSystemRef.current.geometry.attributes.velocity.array as Float32Array;
      const colors = particleSystemRef.current.geometry.attributes.color.array as Float32Array;

      // 根据材料调整动画速度
      const speedMultiplier = (materialProps.animationSpeed || 1.0) * (deltaTime / 16.67);
      const effectIntensity = materialProps.effectIntensity || 1.0;

        for (let i = 0; i < positions.length; i += 3) {
          // 应用速度 - 根据材料调整
          positions[i] += velocities[i] * speedMultiplier;
          positions[i + 1] += velocities[i + 1] * speedMultiplier;
          positions[i + 2] += velocities[i + 2] * speedMultiplier;

          // 重置超出范围的粒子
          if (positions[i + 1] < -0.5) {
            const radius = Math.random() * 0.3 * effectIntensity;
            const angle = Math.random() * Math.PI * 2;
            positions[i] = Math.cos(angle) * radius;
            positions[i + 1] = 1.5 + Math.random() * 0.3;
            positions[i + 2] = Math.sin(angle) * radius;

            // 重新设置速度 - 根据材料调整
            const baseSpeed = 0.02 * effectIntensity;
            velocities[i] = (Math.random() - 0.5) * baseSpeed;
            velocities[i + 1] = -Math.random() * 0.05 * effectIntensity - 0.01;
            velocities[i + 2] = (Math.random() - 0.5) * baseSpeed;

            // 更新颜色 - 使用材料特定颜色
            const particleColor = new THREE.Color(materialProps.particleColor);
            const intensity = Math.random() * effectIntensity;
            colors[i] = particleColor.r * (0.5 + intensity * 0.5);
            colors[i + 1] = particleColor.g * (0.5 + intensity * 0.5);
            colors[i + 2] = particleColor.b * (0.5 + intensity * 0.5);
          }
        }

      // 性能优化：批量更新几何体属性
      particleSystemRef.current.geometry.attributes.position.needsUpdate = true;
      particleSystemRef.current.geometry.attributes.color.needsUpdate = true;

      if (particleSystemRef.current.material instanceof THREE.PointsMaterial) {
        particleSystemRef.current.material.opacity = plasmaIntensityRef.current * 0.9;
      }
    } else if (particleSystemRef.current) {
      particleSystemRef.current.visible = false;
    }

    // 更新工件表面效果 - 材料特定动画，性能优化
    if (workpieceRef.current && plasmaIntensityRef.current > 0) {
      const materialProps = materialPropsCache.current || getMaterialProperties();
      const time = currentTime * 0.001;

      workpieceRef.current.children.forEach((child, index) => {
        if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshPhysicalMaterial) {
          // 根据材料类型应用不同的表面变化效果
          if (materialProps.surfaceActivationEffect) {
            // 聚合物：表面活化效果
            child.material.roughness = Math.max(0.1,
              materialProps.roughness - plasmaIntensityRef.current * 0.3 * Math.sin(time * 2)
            );
          } else if (materialProps.oxideRemoval) {
            // 金属：氧化层去除效果
            child.material.metalness = Math.min(1.0,
              materialProps.metalness + plasmaIntensityRef.current * 0.1 * Math.sin(time * 3)
            );
            child.material.roughness = Math.max(0.05,
              materialProps.roughness + plasmaIntensityRef.current * 0.2 * Math.sin(time * 2.5)
            );
          } else if (materialProps.surfaceCleaning) {
            // 玻璃陶瓷：表面清洁效果
            child.material.clearcoat = Math.min(1.0,
              materialProps.clearcoat + plasmaIntensityRef.current * 0.2 * Math.sin(time * 1.5)
            );
            child.material.opacity = Math.min(1.0,
              materialProps.opacity + plasmaIntensityRef.current * 0.1 * Math.sin(time * 2)
            );
          }
        }
      });
    }

    // 旋转工件 - 性能优化
    if (workpieceRef.current) {
      const materialProps = materialPropsCache.current || getMaterialProperties();
      const rotationSpeed = 0.005 * (materialProps.animationSpeed || 1.0) * (deltaTime / 16.67);
      workpieceRef.current.rotation.y += rotationSpeed;
    }

    // 渲染场景
    rendererRef.current.render(sceneRef.current, cameraRef.current);
    animationIdRef.current = requestAnimationFrame(animate);
  }, []);

  // 初始化Three.js场景
  useEffect(() => {
    if (!mountRef.current) return;

    // Ensure container has dimensions
    const container = mountRef.current;
    const width = container.clientWidth || 800;
    const height = container.clientHeight || 600;

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x1a1a2e);
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(
      75,
      width / height,
      0.1,
      1000
    );
    camera.position.set(5, 5, 5);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // 创建渲染器 - 性能优化配置，使用新的canvas避免上下文冲突
    const canvas = document.createElement('canvas');
    const renderer = new THREE.WebGLRenderer({
      canvas: canvas,
      antialias: window.devicePixelRatio <= 1, // 高DPI设备禁用抗锯齿以提高性能
      alpha: true,
      preserveDrawingBuffer: false, // 禁用以提高性能
      powerPreference: "high-performance", // 使用高性能GPU
      stencil: false, // 禁用模板缓冲区
      depth: true
    });
    renderer.setSize(width, height);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.outputColorSpace = THREE.SRGBColorSpace;

    // 性能优化设置
    renderer.info.autoReset = false; // 手动重置渲染信息
    renderer.sortObjects = true; // 启用对象排序以优化渲染
    renderer.autoClear = true;

    // 清理任何现有的子元素
    while (container.firstChild) {
      container.removeChild(container.firstChild);
    }

    container.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // 添加光源
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // 创建工件
    createWorkpiece();

    // 创建等离子喷枪
    createPlasmaGun();

    // 创建等离子光束
    createPlasmaBeam();

    // 创建粒子系统
    createParticleSystem();

    // 创建地面网格
    createGrid();

    // 开始渲染循环
    animate();

    // 处理窗口大小变化
    const handleResize = () => {
      if (!mountRef.current || !cameraRef.current || !rendererRef.current) return;

      const width = mountRef.current.clientWidth || 800;
      const height = mountRef.current.clientHeight || 600;

      cameraRef.current.aspect = width / height;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(width, height);
      rendererRef.current.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    };

    window.addEventListener('resize', handleResize);

    // Add ResizeObserver for better container resize detection
    let resizeObserver: ResizeObserver | null = null;
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        handleResize();
      });
      resizeObserver.observe(container);
    }

    return () => {
      const currentMount = mountRef.current;
      window.removeEventListener('resize', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      // 清理全局鼠标事件监听器
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);

      // 清理DOM元素
      if (currentMount && renderer.domElement && currentMount.contains(renderer.domElement)) {
        currentMount.removeChild(renderer.domElement);
      }

      // 清理Three.js资源
      if (sceneRef.current) {
        sceneRef.current.clear();
      }
      renderer.dispose();
    };
  }, [animate, createWorkpiece]);



  // 创建等离子喷枪
  const createPlasmaGun = () => {
    if (!sceneRef.current) return;

    const gunGroup = new THREE.Group();

    // 喷枪主体
    const bodyGeometry = new THREE.CylinderGeometry(0.1, 0.15, 1, 16);
    const bodyMaterial = new THREE.MeshPhongMaterial({ color: 0x666666 });
    const gunBody = new THREE.Mesh(bodyGeometry, bodyMaterial);
    gunBody.castShadow = true;
    gunGroup.add(gunBody);

    // 喷嘴
    const nozzleGeometry = new THREE.CylinderGeometry(0.05, 0.08, 0.2, 16);
    const nozzleMaterial = new THREE.MeshPhongMaterial({ color: 0x444444 });
    const nozzle = new THREE.Mesh(nozzleGeometry, nozzleMaterial);
    nozzle.position.y = -0.6;
    gunGroup.add(nozzle);

    gunGroup.position.set(0, 3, 0);
    gunGroup.rotation.x = Math.PI;
    sceneRef.current.add(gunGroup);
    plasmaGunRef.current = gunGroup;
  };

  // 创建等离子光束
  const createPlasmaBeam = () => {
    if (!sceneRef.current) return;

    const beamGroup = new THREE.Group();

    // 主光束
    const beamGeometry = new THREE.CylinderGeometry(0.02, 0.08, 2.5, 8);
    const beamMaterial = new THREE.MeshBasicMaterial({
      color: 0x9C27B0,
      transparent: true,
      opacity: 0.8
    });

    const beam = new THREE.Mesh(beamGeometry, beamMaterial);
    beam.position.y = 1.75;
    beamGroup.add(beam);

    // 内核光束
    const coreGeometry = new THREE.CylinderGeometry(0.01, 0.04, 2.5, 8);
    const coreMaterial = new THREE.MeshBasicMaterial({
      color: 0xFFFFFF,
      transparent: true,
      opacity: 0.9
    });

    const core = new THREE.Mesh(coreGeometry, coreMaterial);
    core.position.y = 1.75;
    beamGroup.add(core);

    beamGroup.position.set(0, 0, 0);
    beamGroup.visible = false;
    sceneRef.current.add(beamGroup);
    plasmaBeamRef.current = beamGroup;
  };

  // 创建粒子系统
  const createParticleSystem = () => {
    if (!sceneRef.current) return;

    const particleCount = 2000;
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);
    const velocities = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;

      // 位置 - 集中在等离子喷枪附近
      const radius = Math.random() * 0.5;
      const angle = Math.random() * Math.PI * 2;
      positions[i3] = Math.cos(angle) * radius;
      positions[i3 + 1] = 1.5 + Math.random() * 0.5;
      positions[i3 + 2] = Math.sin(angle) * radius;

      // 速度
      velocities[i3] = (Math.random() - 0.5) * 0.02;
      velocities[i3 + 1] = -Math.random() * 0.05 - 0.01;
      velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

      // 颜色 - 等离子蓝紫色
      const intensity = Math.random();
      colors[i3] = 0.3 + intensity * 0.4;     // R
      colors[i3 + 1] = 0.1 + intensity * 0.5; // G
      colors[i3 + 2] = 0.8 + intensity * 0.2; // B

      // 大小
      sizes[i] = Math.random() * 0.08 + 0.02;
    }

    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));

    const material = new THREE.PointsMaterial({
      size: 0.03,
      vertexColors: true,
      transparent: true,
      opacity: 0.9,
      blending: THREE.AdditiveBlending,
      sizeAttenuation: true
    });

    const particles = new THREE.Points(geometry, material);
    particles.visible = false;
    sceneRef.current.add(particles);
    particleSystemRef.current = particles;
  };

  // 创建地面网格
  const createGrid = () => {
    if (!sceneRef.current) return;

    const gridHelper = new THREE.GridHelper(10, 20, 0x444444, 0x222222);
    gridHelper.position.y = -1;
    sceneRef.current.add(gridHelper);
  };



  // 鼠标事件处理 - 性能优化版本
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    isMouseDownRef.current = true;
    mousePosRef.current = { x: event.clientX, y: event.clientY };

    // 添加全局鼠标事件监听器以获得更好的响应性
    document.addEventListener('mousemove', handleGlobalMouseMove, { passive: false });
    document.addEventListener('mouseup', handleGlobalMouseUp, { passive: true });
  }, []);

  // 使用全局鼠标移动事件以获得更好的性能
  const handleGlobalMouseMove = useCallback(throttle((event: MouseEvent) => {
    if (!isMouseDownRef.current) return;

    const deltaX = event.clientX - mousePosRef.current.x;
    const deltaY = event.clientY - mousePosRef.current.y;

    // 直接更新ref，避免React重渲染
    cameraRotationRef.current = {
      x: Math.max(-Math.PI / 2, Math.min(Math.PI / 2, cameraRotationRef.current.x - deltaY * 0.01)),
      y: cameraRotationRef.current.y + deltaX * 0.01
    };

    mousePosRef.current = { x: event.clientX, y: event.clientY };
    cameraUpdateNeededRef.current = true;
  }, 8), []); // 8ms节流，约120fps

  const handleGlobalMouseUp = useCallback(() => {
    isMouseDownRef.current = false;
    document.removeEventListener('mousemove', handleGlobalMouseMove);
    document.removeEventListener('mouseup', handleGlobalMouseUp);
  }, [handleGlobalMouseMove]);

  // React事件处理器（保持兼容性）
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    // 这个函数现在主要用于触摸设备的兼容性
    if (!isMouseDownRef.current) return;
    handleGlobalMouseMove(event.nativeEvent);
  }, [handleGlobalMouseMove]);

  const handleMouseUp = useCallback(() => {
    handleGlobalMouseUp();
  }, [handleGlobalMouseUp]);

  // 更新材料属性
  useEffect(() => {
    if (workpieceRef.current && sceneRef.current) {
      // 移除旧的工件
      sceneRef.current.remove(workpieceRef.current);

      // 创建新的工件以应用新材料属性
      createWorkpiece();

      // 添加材料切换动画
      if (workpieceRef.current) {
        workpieceRef.current.scale.set(0.8, 0.8, 0.8);

        // 使用GSAP或简单的动画来缩放回原始大小
        const animateScale = () => {
          if (workpieceRef.current) {
            const currentScale = workpieceRef.current.scale.x;
            if (currentScale < 1.0) {
              workpieceRef.current.scale.setScalar(Math.min(1.0, currentScale + 0.02));
              requestAnimationFrame(animateScale);
            }
          }
        };
        animateScale();
      }
    }
  }, [scene.selectedMaterial, createWorkpiece]);

  // 添加触摸事件支持
  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      isMouseDownRef.current = true;
      mousePosRef.current = { x: touch.clientX, y: touch.clientY };
    }
  }, []);

  const handleTouchMove = useCallback((event: React.TouchEvent) => {
    if (!isMouseDownRef.current || event.touches.length !== 1) return;

    event.preventDefault();
    const touch = event.touches[0];
    const deltaX = touch.clientX - mousePosRef.current.x;
    const deltaY = touch.clientY - mousePosRef.current.y;

    cameraRotationRef.current = {
      x: Math.max(-Math.PI / 2, Math.min(Math.PI / 2, cameraRotationRef.current.x - deltaY * 0.01)),
      y: cameraRotationRef.current.y + deltaX * 0.01
    };

    mousePosRef.current = { x: touch.clientX, y: touch.clientY };
    cameraUpdateNeededRef.current = true;
  }, []);

  const handleTouchEnd = useCallback(() => {
    isMouseDownRef.current = false;
  }, []);

  return (
    <div
      ref={mountRef}
      className={`w-full h-full cursor-grab active:cursor-grabbing ${className || ''}`}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{ touchAction: 'none' }} // 防止触摸滚动
    />
  );
};

export default ThreeScene;
