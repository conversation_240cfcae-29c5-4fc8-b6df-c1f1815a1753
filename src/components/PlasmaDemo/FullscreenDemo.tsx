'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button, Space, Typography, Card, message } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  QuestionCircleOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons';
import { usePlasmaStore, selectIsProcessRunning, selectCurrentMaterial, selectCurrentProcess } from '@/stores/plasmaStore';
import { MaterialType, ProcessType } from '@/types/plasma';
import ThreeScene from './ThreeScene';
import MaterialSelector from './MaterialSelector';
import ProcessControls from './ProcessControls';
import ContactAngleComparison from './ContactAngleComparison';
import ParametersPanel from './ParametersPanel';
import DemoGuide from './DemoGuide';

const { Title, Text } = Typography;

interface FullscreenDemoProps {
  onExitFullscreen: () => void;
}

const FullscreenDemo: React.FC<FullscreenDemoProps> = ({ onExitFullscreen }) => {
  const [showGuide, setShowGuide] = useState(false);
  const [showParameters, setShowParameters] = useState(true);
  const [showComparison, setShowComparison] = useState(true);
  const [leftPanelCollapsed, setLeftPanelCollapsed] = useState(false);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false);

  // Zustand store hooks
  const { scene, setSelectedMaterial, setSelectedProcess, startProcess, stopProcess, resetScene } = usePlasmaStore();
  const isProcessRunning = usePlasmaStore(selectIsProcessRunning);
  const currentMaterial = usePlasmaStore(selectCurrentMaterial);
  const currentProcess = usePlasmaStore(selectCurrentProcess);

  // 自动演示
  const startAutoDemo = useCallback(() => {
    message.info('开始自动演示');
    const firstProcess = ProcessType.ACTIVATION;
    setSelectedProcess(firstProcess);
    setTimeout(() => {
      startProcess();
    }, 1000);
  }, [setSelectedProcess, startProcess]);

  // 重置演示
  const handleReset = useCallback(() => {
    resetScene();
    message.info('演示已重置');
  }, [resetScene]);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (event.code) {
        case 'KeyD':
          if (!isProcessRunning) {
            startAutoDemo();
          }
          break;
        case 'KeyR':
          handleReset();
          break;
        case 'KeyH':
          setShowGuide(true);
          break;
        case 'Escape':
          onExitFullscreen();
          break;
        case 'Digit1':
          setSelectedMaterial(MaterialType.POLYMER);
          break;
        case 'Digit2':
          setSelectedMaterial(MaterialType.METAL);
          break;
        case 'Digit3':
          setSelectedMaterial(MaterialType.GLASS_CERAMIC);
          break;
        case 'KeyP':
          setShowParameters(!showParameters);
          break;
        case 'KeyC':
          setShowComparison(!showComparison);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isProcessRunning, showParameters, showComparison, setSelectedMaterial, onExitFullscreen, handleReset, startAutoDemo]);



  // 处理材料选择
  const handleMaterialSelect = (material: MaterialType) => {
    if (!isProcessRunning) {
      setSelectedMaterial(material);
      message.success(`已选择材料: ${currentMaterial?.displayName}`);
    } else {
      message.warning('处理进行中，无法切换材料');
    }
  };

  // 处理工艺开始
  const handleProcessStart = (process: ProcessType) => {
    setSelectedProcess(process);
    startProcess();
    message.success(`开始 ${currentProcess?.displayName} 处理`);
  };

  // 处理工艺停止
  const handleProcessStop = () => {
    stopProcess();
    message.info('处理已停止');
  };



  return (
    <div className="fullscreen-demo">
      {/* 顶部控制栏 */}
      <div className="fullscreen-header">
        <div className="header-left">
          <Title level={3} style={{ margin: 0, color: '#fff' }}>
            等离子清洗技术3D演示
          </Title>
        </div>
        
        <div className="header-center">
          <Space>
            <Button
              type="primary"
              icon={isProcessRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={isProcessRunning ? handleProcessStop : startAutoDemo}
              size="small"
            >
              {isProcessRunning ? '停止' : '演示'}
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
              disabled={isProcessRunning}
              size="small"
            >
              重置
            </Button>
            <Button
              icon={<QuestionCircleOutlined />}
              onClick={() => setShowGuide(true)}
              size="small"
            >
              帮助
            </Button>
          </Space>
        </div>

        <div className="header-right">
          <Space>
            <Button
              icon={<FullscreenExitOutlined />}
              onClick={onExitFullscreen}
              size="small"
            >
              退出全屏
            </Button>
          </Space>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="fullscreen-content">
        {/* 左侧面板 */}
        <div className={`left-panel ${leftPanelCollapsed ? 'collapsed' : ''}`}>
          <div className="panel-header">
            <Text strong style={{ color: '#fff' }}>材料控制</Text>
            <Button
              type="text"
              size="small"
              icon={leftPanelCollapsed ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={() => setLeftPanelCollapsed(!leftPanelCollapsed)}
              style={{ color: '#fff' }}
            />
          </div>
          
          {!leftPanelCollapsed && (
            <div className="panel-content">
              <Card
                title="材料选择"
                size="small"
                style={{ marginBottom: 16 }}
              >
                <MaterialSelector
                  selectedMaterial={scene.selectedMaterial}
                  onMaterialSelect={handleMaterialSelect}
                  disabled={isProcessRunning}
                />
              </Card>

              <Card
                title="工艺控制"
                size="small"
              >
                <ProcessControls
                  selectedProcess={scene.selectedProcess}
                  onProcessStart={handleProcessStart}
                  disabled={false}
                  isRunning={isProcessRunning}
                />
              </Card>
            </div>
          )}
        </div>

        {/* 中央3D场景 */}
        <div className="center-scene">
          <div className="scene-container">
            <div className="scene-header">
              <Text strong style={{ color: '#fff', fontSize: '16px' }}>
                3D演示场景
              </Text>
              <Text style={{ color: 'rgba(255, 255, 255, 0.7)', fontSize: '12px' }}>
                拖拽旋转视角 | 滚轮缩放
              </Text>
            </div>
            <div className="scene-content">
              <ThreeScene className="fullscreen-three-scene" />
            </div>
          </div>
        </div>

        {/* 右侧面板 */}
        <div className={`right-panel ${rightPanelCollapsed ? 'collapsed' : ''}`}>
          <div className="panel-header">
            <Text strong style={{ color: '#fff' }}>数据监控</Text>
            <Button
              type="text"
              size="small"
              icon={rightPanelCollapsed ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={() => setRightPanelCollapsed(!rightPanelCollapsed)}
              style={{ color: '#fff' }}
            />
          </div>
          
          {!rightPanelCollapsed && (
            <div className="panel-content">
              {showParameters && (
                <Card
                  title="参数监控"
                  size="small"
                  style={{ marginBottom: 16 }}
                  extra={
                    <Button
                      type="text"
                      size="small"
                      icon={<EyeInvisibleOutlined />}
                      onClick={() => setShowParameters(false)}
                    />
                  }
                >
                  <ParametersPanel />
                </Card>
              )}

              {showComparison && (
                <Card
                  title="性能对比"
                  size="small"
                  extra={
                    <Button
                      type="text"
                      size="small"
                      icon={<EyeInvisibleOutlined />}
                      onClick={() => setShowComparison(false)}
                    />
                  }
                >
                  <ContactAngleComparison />
                </Card>
              )}

              {!showParameters && (
                <Button
                  block
                  icon={<EyeOutlined />}
                  onClick={() => setShowParameters(true)}
                  style={{ marginBottom: 8 }}
                >
                  显示参数监控
                </Button>
              )}

              {!showComparison && (
                <Button
                  block
                  icon={<EyeOutlined />}
                  onClick={() => setShowComparison(true)}
                >
                  显示性能对比
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 使用指南弹窗 */}
      <DemoGuide
        open={showGuide}
        onClose={() => setShowGuide(false)}
        onStartDemo={startAutoDemo}
      />

      <style jsx>{`
        .fullscreen-demo {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
          z-index: 9999;
          display: flex;
          flex-direction: column;
        }

        .fullscreen-header {
          height: 60px;
          background: rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(10px);
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 20px;
          flex-shrink: 0;
        }

        .header-left,
        .header-center,
        .header-right {
          flex: 1;
          display: flex;
          align-items: center;
        }

        .header-center {
          justify-content: center;
        }

        .header-right {
          justify-content: flex-end;
        }

        .fullscreen-content {
          flex: 1;
          display: flex;
          height: calc(100vh - 60px);
          overflow: hidden;
        }

        .left-panel,
        .right-panel {
          width: 320px;
          background: rgba(0, 0, 0, 0.2);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          display: flex;
          flex-direction: column;
          transition: all 0.3s ease;
        }

        .left-panel.collapsed,
        .right-panel.collapsed {
          width: 60px;
        }

        .panel-header {
          height: 50px;
          padding: 0 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          background: rgba(0, 0, 0, 0.2);
        }

        .panel-content {
          flex: 1;
          padding: 16px;
          overflow-y: auto;
        }

        .center-scene {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
        }

        .scene-container {
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.1);
          border-radius: 12px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          overflow: hidden;
          position: relative;
          display: flex;
          flex-direction: column;
        }

        .scene-header {
          height: 50px;
          padding: 0 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: rgba(0, 0, 0, 0.3);
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          flex-shrink: 0;
        }

        .scene-content {
          flex: 1;
          position: relative;
          overflow: hidden;
        }

        .fullscreen-three-scene {
          width: 100%;
          height: 100%;
          border-radius: 0;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
          .left-panel,
          .right-panel {
            width: 280px;
          }
        }

        @media (max-width: 768px) {
          .fullscreen-header {
            height: 50px;
            padding: 0 10px;
          }

          .header-left,
          .header-center,
          .header-right {
            flex: none;
          }

          .left-panel,
          .right-panel {
            width: 250px;
          }

          .center-scene {
            padding: 10px;
          }

          .scene-header {
            height: 40px;
            padding: 0 15px;
          }
        }

        /* 动画效果 */
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: scale(0.95);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }

        .fullscreen-demo {
          animation: fadeIn 0.3s ease-out;
        }

        .left-panel,
        .right-panel {
          transition: width 0.3s ease, opacity 0.3s ease;
        }

        .left-panel.collapsed,
        .right-panel.collapsed {
          opacity: 0.8;
        }

        .scene-container {
          transition: all 0.3s ease;
        }

        .scene-container:hover {
          border-color: rgba(255, 255, 255, 0.2);
          box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        /* Card组件深色主题样式 */
        :global(.ant-card) {
          background: rgba(255, 255, 255, 0.08) !important;
          border: 1px solid rgba(255, 255, 255, 0.12) !important;
          border-radius: 12px !important;
          backdrop-filter: blur(10px) !important;
        }

        :global(.ant-card-head) {
          background: transparent !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.12) !important;
          color: #ffffff !important;
        }

        :global(.ant-card-head-title) {
          color: #ffffff !important;
          font-weight: 600 !important;
        }

        :global(.ant-card-body) {
          background: transparent !important;
          color: #ffffff !important;
        }

        /* 按钮样式 */
        :global(.ant-btn-primary) {
          background: #9C27B0 !important;
          border-color: #9C27B0 !important;
        }

        :global(.ant-btn-primary:hover) {
          background: #7B1FA2 !important;
          border-color: #7B1FA2 !important;
        }

        /* 进度条样式 */
        :global(.ant-progress-bg) {
          background: #9C27B0 !important;
        }

        /* 标签样式 */
        :global(.ant-tag) {
          background: rgba(156, 39, 176, 0.2) !important;
          border-color: #9C27B0 !important;
          color: #ffffff !important;
        }

        /* 文字颜色 */
        :global(.ant-typography) {
          color: #ffffff !important;
        }

        :global(.ant-typography-caption) {
          color: #b0b0b0 !important;
        }
      `}</style>
    </div>
  );
};

export default FullscreenDemo;
