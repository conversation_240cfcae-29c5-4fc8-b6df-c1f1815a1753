import React from 'react';
import { Typography, Statistic } from 'antd';
import { usePlasmaStore, selectCurrentMaterial, selectContactAngleData } from '@/stores/plasmaStore';

const { Text } = Typography;

const FloatingPerformancePanel: React.FC = () => {
  const currentMaterial = usePlasmaStore(selectCurrentMaterial);
  const contactAngleData = usePlasmaStore(selectContactAngleData);

  if (!currentMaterial || !contactAngleData) {
    return null; // Don't show if no material selected
  }

  const improvement = ((contactAngleData.before - contactAngleData.after) / contactAngleData.before * 100);

  return (
    <div className="floating-performance-panel">
      {/* 标题 */}
      <div className="panel-header">
        <Text strong style={{ fontSize: '12px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
          性能改善数据
        </Text>
      </div>

      {/* 统计数据网格 */}
      <div className="statistics-grid">
        <div className="stat-item">
          <Statistic
            title={<span style={{ color: '#d0d0d0', fontSize: '9px' }}>处理前接触角</span>}
            value={contactAngleData.before}
            suffix="°"
            valueStyle={{ color: '#3498db', fontSize: '14px', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}
          />
        </div>
        
        <div className="stat-item">
          <Statistic
            title={<span style={{ color: '#d0d0d0', fontSize: '9px' }}>处理后接触角</span>}
            value={contactAngleData.after}
            suffix="°"
            valueStyle={{ color: '#2ecc71', fontSize: '14px', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}
          />
        </div>
        
        <div className="stat-item">
          <Statistic
            title={<span style={{ color: '#d0d0d0', fontSize: '9px' }}>改善程度</span>}
            value={improvement.toFixed(1)}
            suffix="%"
            valueStyle={{ color: '#e74c3c', fontSize: '14px', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}
          />
        </div>
      </div>

      {/* 应用效果 */}
      <div className="effects-summary">
        <div className="effect-item">
          <Text style={{ fontSize: '10px', color: '#52c41a', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            ✓ 粘接强度提升
          </Text>
        </div>
        <div className="effect-item">
          <Text style={{ fontSize: '10px', color: '#52c41a', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            ✓ 涂层附着力改善
          </Text>
        </div>
        <div className="effect-item">
          <Text style={{ fontSize: '10px', color: '#52c41a', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
            ✓ 清洁度提高
          </Text>
        </div>
      </div>

      <style jsx>{`
        .floating-performance-panel {
          position: absolute;
          bottom: 20px;
          right: 10px;
          background: rgba(0, 0, 0, 0.25);
          backdrop-filter: blur(6px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          padding: 4px 6px;
          box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
          z-index: 40;
          min-width: 140px;
          max-width: 160px;
          pointer-events: auto;
          transition: all 0.3s ease;
        }

        .floating-performance-panel:hover {
          background: rgba(0, 0, 0, 0.5);
          border-color: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
          min-width: 180px;
          max-width: 220px;
        }

        .panel-header {
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .statistics-grid {
          display: grid;
          grid-template-columns: 1fr;
          gap: 8px;
          margin-bottom: 12px;
        }

        .stat-item {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 6px;
          padding: 8px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          text-align: center;
          transition: all 0.2s ease;
        }

        .stat-item:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
        }

        .effects-summary {
          background: rgba(82, 196, 26, 0.1);
          border-radius: 6px;
          padding: 8px;
          border: 1px solid rgba(82, 196, 26, 0.2);
        }

        .effect-item {
          margin: 2px 0;
          padding: 2px 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .floating-performance-panel {
            bottom: 12px;
            right: 8px;
            min-width: 90px;
            max-width: 110px;
            padding: 4px 6px;
          }
        }

        @media (max-width: 480px) {
          .floating-performance-panel {
            bottom: 8px;
            right: 6px;
            min-width: 80px;
            max-width: 100px;
            padding: 3px 4px;
          }
          
          .stat-item {
            padding: 6px;
          }
          
          .statistics-grid {
            gap: 6px;
          }
        }
      `}</style>
    </div>
  );
};

export default FloatingPerformancePanel;
