import React, { useEffect, useState } from 'react';
import { Typography, Progress, Tag } from 'antd';
import { ThunderboltOutlined, DashboardOutlined, RocketOutlined } from '@ant-design/icons';
import { usePlasmaStore, selectCurrentProcess, selectCurrentMaterial, selectRemainingTime, selectAnimationState } from '@/stores/plasmaStore';
import { MATERIAL_SPECIFIC_PARAMETERS } from '@/constants/plasma';
import { AnimationState } from '@/types/plasma';

const { Text } = Typography;

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

const FloatingParametersPanel: React.FC = () => {
  const { realTimeParams, scene } = usePlasmaStore();
  const currentProcess = usePlasmaStore(selectCurrentProcess);
  const currentMaterial = usePlasmaStore(selectCurrentMaterial);
  const remainingTime = usePlasmaStore(selectRemainingTime);
  const animationState = usePlasmaStore(selectAnimationState);
  const [animatedParams, setAnimatedParams] = useState(realTimeParams);

  // 获取当前材料和工艺的特定参数
  const getMaterialSpecificParams = () => {
    if (!currentMaterial || !currentProcess) return null;
    return (MATERIAL_SPECIFIC_PARAMETERS as Record<string, Record<string, any>>)[currentMaterial.id]?.[currentProcess.id] || currentProcess.parameters;
  };

  const materialParams = getMaterialSpecificParams();

  // 实时参数动画效果 - 基于材料特定参数
  useEffect(() => {
    if (scene.isProcessRunning && materialParams) {
      const interval = setInterval(() => {
        const variance = materialParams.effectIntensity || 1.0;
        setAnimatedParams(() => ({
          power: materialParams.power + (Math.random() - 0.5) * 10 * variance,
          gasFlow: materialParams.gasFlow + (Math.random() - 0.5) * 5 * variance,
          speed: materialParams.speed + (Math.random() - 0.5) * 2 * variance,
          timestamp: Date.now()
        }));
      }, 500 / (materialParams.animationSpeed || 1.0));

      return () => clearInterval(interval);
    } else if (materialParams) {
      setAnimatedParams({
        power: materialParams.power,
        gasFlow: materialParams.gasFlow,
        speed: materialParams.speed,
        timestamp: Date.now()
      });
    }
  }, [scene.isProcessRunning, materialParams]);

  if (!currentProcess || !materialParams) {
    return null; // Don't show if no process selected
  }

  const getParameterStatus = (current: number, target: number, tolerance: number = 5) => {
    const diff = Math.abs(current - target);
    if (diff <= tolerance) return 'success';
    if (diff <= tolerance * 2) return 'warning';
    return 'error';
  };

  const getParameterColor = (status: string) => {
    switch (status) {
      case 'success': return '#52c41a';
      case 'warning': return '#faad14';
      case 'error': return '#ff4d4f';
      default: return '#1890ff';
    }
  };

  return (
    <div className="floating-parameters-panel">
      {/* 当前工艺信息 */}
      <div className="process-info">
        <Text strong style={{ fontSize: '12px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
          {currentProcess.displayName}
        </Text>
        <Tag color="purple" style={{ marginLeft: '6px', fontSize: '12px' }}>
          {scene.isProcessRunning ? '运行中' : '待机'}
        </Tag>
      </div>

      {/* 关键参数监控 */}
      <div className="key-parameters">
        {/* 功率 */}
        <div className="param-item">
          <div className="param-header">
            <ThunderboltOutlined style={{ marginRight: '4px', color: '#faad14' }} />
            <Text style={{ fontSize: '10px', color: '#d0d0d0' }}>功率</Text>
          </div>
          <Text strong style={{
            fontSize: '14px',
            color: getParameterColor(getParameterStatus(animatedParams.power, materialParams.power)),
            textShadow: '0 1px 2px rgba(0,0,0,0.8)'
          }}>
            {scene.isProcessRunning ? animatedParams.power.toFixed(1) : materialParams.power}W
          </Text>
          <Progress
            percent={(animatedParams.power / materialParams.power) * 100}
            strokeColor={getParameterColor(getParameterStatus(animatedParams.power, materialParams.power))}
            size="small"
            showInfo={false}
            strokeWidth={2}
          />
        </div>

        {/* 气体流量 */}
        <div className="param-item">
          <div className="param-header">
            <DashboardOutlined style={{ marginRight: '4px', color: '#1890ff' }} />
            <Text style={{ fontSize: '10px', color: '#d0d0d0' }}>气流</Text>
          </div>
          <Text strong style={{
            fontSize: '14px',
            color: getParameterColor(getParameterStatus(animatedParams.gasFlow, materialParams.gasFlow)),
            textShadow: '0 1px 2px rgba(0,0,0,0.8)'
          }}>
            {scene.isProcessRunning ? animatedParams.gasFlow.toFixed(1) : materialParams.gasFlow}
          </Text>
          <Progress
            percent={(animatedParams.gasFlow / materialParams.gasFlow) * 100}
            strokeColor={getParameterColor(getParameterStatus(animatedParams.gasFlow, materialParams.gasFlow))}
            size="small"
            showInfo={false}
            strokeWidth={2}
          />
        </div>

        {/* 处理速度 */}
        <div className="param-item">
          <div className="param-header">
            <RocketOutlined style={{ marginRight: '4px', color: '#52c41a' }} />
            <Text style={{ fontSize: '10px', color: '#d0d0d0' }}>速度</Text>
          </div>
          <Text strong style={{
            fontSize: '14px',
            color: getParameterColor(getParameterStatus(animatedParams.speed, materialParams.speed)),
            textShadow: '0 1px 2px rgba(0,0,0,0.8)'
          }}>
            {scene.isProcessRunning ? animatedParams.speed.toFixed(1) : materialParams.speed}
          </Text>
          <Progress
            percent={(animatedParams.speed / materialParams.speed) * 100}
            strokeColor={getParameterColor(getParameterStatus(animatedParams.speed, materialParams.speed))}
            size="small"
            showInfo={false}
            strokeWidth={2}
          />
        </div>
      </div>

      {/* 工艺状态 */}
      <div className="status-info">
        <div className="status-item">
          <Text style={{ fontSize: '10px', color: '#d0d0d0' }}>进度:</Text>
          <Text strong style={{ fontSize: '10px', marginLeft: '4px', color: '#9C27B0' }}>
            {scene.processProgress.toFixed(0)}%
          </Text>
        </div>

        {(animationState === AnimationState.RUNNING || animationState === AnimationState.PAUSED) && (
          <div className="status-item">
            <Text style={{ fontSize: '10px', color: '#d0d0d0' }}>剩余:</Text>
            <Text strong style={{ fontSize: '10px', marginLeft: '4px', color: '#ff9800' }}>
              {formatTime(remainingTime)}
            </Text>
          </div>
        )}
      </div>

      <style jsx>{`
        .floating-parameters-panel {
          position: absolute;
          top: 20px;
          left: 20px;
          background: rgba(0, 0, 0, 0.25);
          backdrop-filter: blur(6px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          padding: 4px 6px;
          box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
          z-index: 40;
          min-width: 140px;
          max-width: 160px;
          pointer-events: auto;
          transition: all 0.3s ease;
        }

        .floating-parameters-panel:hover {
          background: rgba(0, 0, 0, 0.5);
          border-color: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
          min-width: 180px;
          max-width: 220px;
        }

        .process-info {
          display: flex;
          align-items: center;
          margin-bottom: 6px;
          padding-bottom: 4px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .key-parameters {
          display: flex;
          flex-direction: column;
          gap: 4px;
          margin-bottom: 6px;
        }

        .param-item {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 4px;
          padding: 4px 6px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .param-header {
          display: flex;
          align-items: center;
          margin-bottom: 2px;
        }

        .status-info {
          display: flex;
          justify-content: space-between;
          gap: 12px;
        }

        .status-item {
          display: flex;
          align-items: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .floating-parameters-panel {
            top: 16px;
            left: 16px;
            min-width: 100px;
            max-width: 130px;
            padding: 4px 6px;
          }

          .floating-parameters-panel:hover {
            min-width: 160px;
            max-width: 180px;
          }
        }

        @media (max-width: 480px) {
          .floating-parameters-panel {
            top: 12px;
            left: 12px;
            min-width: 90px;
            max-width: 110px;
            padding: 3px 4px;
          }

          .floating-parameters-panel:hover {
            min-width: 140px;
            max-width: 160px;
          }

          .key-parameters {
            gap: 3px;
          }

          .param-item {
            padding: 3px 4px;
          }
        }
      `}</style>
    </div>
  );
};

export default FloatingParametersPanel;
