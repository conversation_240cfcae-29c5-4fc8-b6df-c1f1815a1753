import React, { useEffect, useState } from 'react';
import { Typography, Space, Statistic, Progress, Tag } from 'antd';
import { ThunderboltOutlined, DashboardOutlined, RocketOutlined, ExperimentOutlined, FireOutlined } from '@ant-design/icons';
import { usePlasmaStore, selectCurrentProcess, selectCurrentMaterial, selectRemainingTime, selectAnimationState } from '@/stores/plasmaStore';
import { MATERIAL_SPECIFIC_PARAMETERS } from '@/constants/plasma';
import { AnimationState } from '@/types/plasma';

const { Text } = Typography;

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

const ParametersPanel: React.FC = () => {
  const { realTimeParams, scene } = usePlasmaStore();
  const currentProcess = usePlasmaStore(selectCurrentProcess);
  const currentMaterial = usePlasmaStore(selectCurrentMaterial);
  const remainingTime = usePlasmaStore(selectRemainingTime);
  const animationState = usePlasmaStore(selectAnimationState);
  const [animatedParams, setAnimatedParams] = useState(realTimeParams);

  // 获取当前材料和工艺的特定参数
  const getMaterialSpecificParams = () => {
    if (!currentMaterial || !currentProcess) return null;
    return (MATERIAL_SPECIFIC_PARAMETERS as Record<string, Record<string, any>>)[currentMaterial.id]?.[currentProcess.id] || currentProcess.parameters;
  };

  const materialParams = getMaterialSpecificParams();

  // 实时参数动画效果 - 基于材料特定参数
  useEffect(() => {
    if (scene.isProcessRunning && materialParams) {
      const interval = setInterval(() => {
        const variance = materialParams.effectIntensity || 1.0;
        setAnimatedParams(() => ({
          power: materialParams.power + (Math.random() - 0.5) * 10 * variance,
          gasFlow: materialParams.gasFlow + (Math.random() - 0.5) * 5 * variance,
          speed: materialParams.speed + (Math.random() - 0.5) * 2 * variance,
          timestamp: Date.now()
        }));
      }, 500 / (materialParams.animationSpeed || 1.0));

      return () => clearInterval(interval);
    } else if (materialParams) {
      setAnimatedParams({
        power: materialParams.power,
        gasFlow: materialParams.gasFlow,
        speed: materialParams.speed,
        timestamp: Date.now()
      });
    }
  }, [scene.isProcessRunning, materialParams]);

  if (!currentProcess || !materialParams) {
    return (
      <div style={{ padding: '16px', textAlign: 'center' }}>
        <Text style={{ color: '#b0b0b0' }}>请选择材料和工艺以查看参数</Text>
      </div>
    );
  }

  const getParameterStatus = (current: number, target: number, tolerance: number = 5) => {
    const diff = Math.abs(current - target);
    if (diff <= tolerance) return 'success';
    if (diff <= tolerance * 2) return 'warning';
    return 'error';
  };

  const getParameterColor = (status: string) => {
    switch (status) {
      case 'success': return '#52c41a';
      case 'warning': return '#faad14';
      case 'error': return '#ff4d4f';
      default: return '#1890ff';
    }
  };

  return (
    <div className="parameters-panel">
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {/* 当前工艺信息 */}
        <div className="process-info-card">
          <div className="process-info">
            <div className="process-header">
              <Text strong style={{ fontSize: '14px', color: '#ffffff' }}>
                {currentProcess.displayName} - {currentMaterial?.displayName}
              </Text>
              <Tag color="purple" style={{ marginLeft: '8px' }}>
                {scene.isProcessRunning ? '运行中' : '待机'}
              </Tag>
            </div>
            <Text style={{ fontSize: '11px', color: '#b0b0b0' }}>
              {currentProcess.description}
            </Text>
            <div style={{ marginTop: '8px' }}>
              <Tag color="blue" style={{ fontSize: '12px' }}>
                优化参数：{currentMaterial?.displayName}
              </Tag>
            </div>
          </div>
        </div>

        {/* 实时参数监控 */}
        <div className="parameters-card">
          <div className="card-title">实时参数监控</div>
          <div className="parameters-grid">
            {/* 功率 */}
            <div className="parameter-item">
              <Statistic
                title={
                  <span>
                    <ThunderboltOutlined style={{ marginRight: '4px' }} />
                    功率
                  </span>
                }
                value={scene.isProcessRunning ? animatedParams.power.toFixed(1) : materialParams.power}
                suffix="W"
                valueStyle={{
                  fontSize: '16px',
                  color: getParameterColor(getParameterStatus(animatedParams.power, materialParams.power)),
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)'
                }}
              />
              <Progress
                percent={(animatedParams.power / materialParams.power) * 100}
                strokeColor={getParameterColor(getParameterStatus(animatedParams.power, materialParams.power))}
                size="small"
                showInfo={false}
              />
            </div>

            {/* 气体流量 */}
            <div className="parameter-item">
              <Statistic
                title={
                  <span>
                    <DashboardOutlined style={{ marginRight: '4px' }} />
                    气流
                  </span>
                }
                value={scene.isProcessRunning ? animatedParams.gasFlow.toFixed(1) : materialParams.gasFlow}
                suffix="sccm"
                valueStyle={{
                  fontSize: '16px',
                  color: getParameterColor(getParameterStatus(animatedParams.gasFlow, materialParams.gasFlow)),
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)'
                }}
              />
              <Progress
                percent={(animatedParams.gasFlow / materialParams.gasFlow) * 100}
                strokeColor={getParameterColor(getParameterStatus(animatedParams.gasFlow, materialParams.gasFlow))}
                size="small"
                showInfo={false}
              />
            </div>

            {/* 处理速度 */}
            <div className="parameter-item">
              <Statistic
                title={
                  <span>
                    <RocketOutlined style={{ marginRight: '4px' }} />
                    速度
                  </span>
                }
                value={scene.isProcessRunning ? animatedParams.speed.toFixed(1) : materialParams.speed}
                suffix="mm/min"
                valueStyle={{
                  fontSize: '16px',
                  color: getParameterColor(getParameterStatus(animatedParams.speed, materialParams.speed)),
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)'
                }}
              />
              <Progress
                percent={(animatedParams.speed / materialParams.speed) * 100}
                strokeColor={getParameterColor(getParameterStatus(animatedParams.speed, materialParams.speed))}
                size="small"
                showInfo={false}
              />
            </div>

            {/* 工作气体 */}
            <div className="parameter-item">
              <div className="gas-info">
                <Text strong style={{ fontSize: '12px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
                  <ExperimentOutlined style={{ marginRight: '4px' }} />
                  工作气体
                </Text>
                <Tag color="purple" style={{ marginTop: '4px' }}>
                  {materialParams.gas}
                </Tag>
              </div>
            </div>

            {/* 效果强度 */}
            <div className="parameter-item">
              <Statistic
                title={
                  <span>
                    <FireOutlined style={{ marginRight: '4px' }} />
                    强度
                  </span>
                }
                value={(materialParams.effectIntensity * 100).toFixed(0)}
                suffix="%"
                valueStyle={{
                  fontSize: '16px',
                  color: '#ff9800',
                  textShadow: '0 1px 2px rgba(0,0,0,0.8)'
                }}
              />
              <Progress
                percent={materialParams.effectIntensity * 100}
                strokeColor="#ff9800"
                size="small"
                showInfo={false}
              />
            </div>
          </div>
        </div>

        {/* 工艺状态 */}
        <div className="status-card">
          <div className="card-title">工艺状态</div>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <div className="status-item">
              <Text strong style={{ fontSize: '11px', color: '#ffffff' }}>处理进度:</Text>
              <Progress
                percent={scene.processProgress}
                status={scene.processProgress === 100 ? 'success' : 'active'}
                strokeColor="#9C27B0"
                size="small"
              />
            </div>

            <div className="status-item">
              <Text strong style={{ fontSize: '11px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>总时长:</Text>
              <Text style={{ fontSize: '11px', marginLeft: '8px', color: '#b0b0b0' }}>
                {currentProcess.duration}秒
              </Text>
            </div>

            {(animationState === AnimationState.RUNNING || animationState === AnimationState.PAUSED) && (
              <div className="status-item">
                <Text strong style={{ fontSize: '11px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>剩余时间:</Text>
                <Text style={{ fontSize: '11px', marginLeft: '8px', color: '#ff9800' }}>
                  {formatTime(remainingTime)}
                </Text>
              </div>
            )}

            <div className="status-item">
              <Text strong style={{ fontSize: '11px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>系统状态:</Text>
              <Tag
                color={
                  animationState === AnimationState.RUNNING ? 'processing' :
                  animationState === AnimationState.PAUSED ? 'warning' :
                  animationState === AnimationState.COMPLETED ? 'success' : 'default'
                }
                style={{ marginLeft: '8px' }}
              >
                {animationState === AnimationState.RUNNING ? '运行中' :
                 animationState === AnimationState.PAUSED ? '已暂停' :
                 animationState === AnimationState.COMPLETED ? '已完成' : '待机'}
              </Tag>
            </div>

            {materialParams.pressure && (
              <div className="status-item">
                <Text strong style={{ fontSize: '11px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>工作压力:</Text>
                <Text style={{ fontSize: '11px', marginLeft: '8px', color: '#b0b0b0' }}>
                  {materialParams.pressure} Torr
                </Text>
              </div>
            )}

            {materialParams.temperature && (
              <div className="status-item">
                <Text strong style={{ fontSize: '11px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>工作温度:</Text>
                <Text style={{ fontSize: '11px', marginLeft: '8px', color: '#b0b0b0' }}>
                  {materialParams.temperature}°C
                </Text>
              </div>
            )}

            <div className="status-item">
              <Text strong style={{ fontSize: '11px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>表面变化率:</Text>
              <Text style={{ fontSize: '11px', marginLeft: '8px', color: '#4CAF50' }}>
                {((materialParams.surfaceChangeRate || 0.5) * 100).toFixed(0)}%
              </Text>
            </div>
          </Space>
        </div>

        {/* 安全提示 */}
        <div className="safety-card">
          <div className="card-title">安全提示</div>
          <Space direction="vertical" size="small">
            <Text style={{ fontSize: '10px', color: '#faad14' }}>
              ⚠️ 等离子处理过程中请勿打开设备
            </Text>
            <Text style={{ fontSize: '10px', color: '#faad14' }}>
              ⚠️ 确保工作区域通风良好
            </Text>
            <Text style={{ fontSize: '10px', color: '#faad14' }}>
              ⚠️ 处理完成后等待冷却再取出工件
            </Text>
          </Space>
        </div>
      </Space>

      <style jsx>{`
        .parameters-panel {
          width: 100%;
          max-height: 600px;
          overflow-y: auto;
          padding-right: 4px;
          scrollbar-width: thin;
          scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }

        .parameters-panel::-webkit-scrollbar {
          width: 6px;
        }

        .parameters-panel::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.05);
          border-radius: 3px;
        }

        .parameters-panel::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
        }

        .parameters-panel::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.3);
        }

        .process-info {
          padding: 4px;
        }

        .process-header {
          display: flex;
          align-items: center;
          margin-bottom: 6px;
          flex-wrap: wrap;
          gap: 8px;
        }

        .parameters-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;
        }

        .parameter-item {
          padding: 8px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 6px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.2s ease;
        }

        .parameter-item:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
        }

        .process-info-card,
        .parameters-card,
        .status-card,
        .safety-card {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          padding: 12px;
          margin-bottom: 12px;
        }

        .card-title {
          color: #ffffff;
          font-size: 12px;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .safety-card {
          border-color: #faad14;
        }

        .gas-info {
          text-align: center;
          padding: 8px;
        }

        .status-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 4px 0;
          flex-wrap: wrap;
          gap: 8px;
        }

        @media (max-width: 768px) {
          .parameters-panel {
            max-height: 500px;
          }

          .parameters-grid {
            grid-template-columns: 1fr;
            gap: 8px;
          }

          .process-header {
            flex-direction: column;
            align-items: flex-start;
          }

          .status-item {
            flex-direction: column;
            align-items: flex-start;
          }
        }

        @media (max-width: 480px) {
          .parameters-panel {
            max-height: 400px;
          }
        }
      `}</style>
    </div>
  );
};

export default ParametersPanel;
