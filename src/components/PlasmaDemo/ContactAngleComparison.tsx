import React, { useRef, useEffect } from 'react';
import { Card, Typography, Space, Statistic, Progress } from 'antd';
import { usePlasmaStore, selectCurrentMaterial, selectContactAngleData } from '@/stores/plasmaStore';

const { Text } = Typography;

const ContactAngleComparison: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const currentMaterial = usePlasmaStore(selectCurrentMaterial);
  const contactAngleData = usePlasmaStore(selectContactAngleData);

  // 绘制水滴角演示
  useEffect(() => {
    if (!canvasRef.current || !contactAngleData) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置canvas尺寸
    canvas.width = 300;
    canvas.height = 150;

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制背景
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制分割线
    ctx.strokeStyle = '#dee2e6';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(canvas.width / 2, 0);
    ctx.lineTo(canvas.width / 2, canvas.height);
    ctx.stroke();
    ctx.setLineDash([]);

    // 绘制标题
    ctx.fillStyle = '#495057';
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('处理前', canvas.width / 4, 20);
    ctx.fillText('处理后', (canvas.width * 3) / 4, 20);

    // 绘制表面
    const surfaceY = canvas.height - 30;
    ctx.strokeStyle = '#6c757d';
    ctx.lineWidth = 2;
    
    // 左侧表面（处理前）
    ctx.beginPath();
    ctx.moveTo(20, surfaceY);
    ctx.lineTo(canvas.width / 2 - 20, surfaceY);
    ctx.stroke();
    
    // 右侧表面（处理后）
    ctx.beginPath();
    ctx.moveTo(canvas.width / 2 + 20, surfaceY);
    ctx.lineTo(canvas.width - 20, surfaceY);
    ctx.stroke();

    // 绘制水滴
    const dropletCenterX1 = canvas.width / 4;
    const dropletCenterX2 = (canvas.width * 3) / 4;
    const dropletRadius = 20;

    // 处理前的水滴（高接触角）
    const beforeAngle = contactAngleData.before;
    drawDroplet(ctx, dropletCenterX1, surfaceY, dropletRadius, beforeAngle, '#3498db');
    
    // 处理后的水滴（低接触角）
    const afterAngle = contactAngleData.after;
    drawDroplet(ctx, dropletCenterX2, surfaceY, dropletRadius, afterAngle, '#2ecc71');

    // 绘制角度标注
    ctx.fillStyle = '#495057';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${beforeAngle}°`, dropletCenterX1, surfaceY + 20);
    ctx.fillText(`${afterAngle}°`, dropletCenterX2, surfaceY + 20);

  }, [contactAngleData]);

  // 绘制水滴的函数
  const drawDroplet = (
    ctx: CanvasRenderingContext2D,
    centerX: number,
    surfaceY: number,
    radius: number,
    contactAngle: number,
    color: string
  ) => {
    ctx.fillStyle = color;
    ctx.strokeStyle = color;
    ctx.lineWidth = 1;

    // 根据接触角计算水滴形状，确保值为正数
    const angleRad = Math.max(0.1, Math.min(Math.PI - 0.1, (contactAngle * Math.PI) / 180));
    const dropletHeight = Math.abs(radius * Math.sin(angleRad));
    const dropletWidth = Math.abs(radius * Math.cos(angleRad));

    // 确保尺寸不为零
    const safeHeight = Math.max(1, dropletHeight);
    const safeWidth = Math.max(1, dropletWidth);

    // 绘制水滴主体
    ctx.beginPath();
    ctx.ellipse(
      centerX,
      surfaceY - safeHeight / 2,
      safeWidth,
      safeHeight,
      0,
      0,
      Math.PI * 2
    );
    ctx.fill();

    // 绘制接触角线
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(centerX - safeWidth, surfaceY);
    ctx.lineTo(centerX, surfaceY - safeHeight);
    ctx.stroke();

    // 绘制角度弧线
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 1;
    ctx.beginPath();
    const arcRadius = Math.min(15, safeWidth * 0.8);
    ctx.arc(centerX - safeWidth, surfaceY, arcRadius, -angleRad, 0);
    ctx.stroke();
  };

  if (!currentMaterial || !contactAngleData) {
    return (
      <Card size="small">
        <Text type="secondary">请选择材料以查看接触角对比</Text>
      </Card>
    );
  }

  const improvement = ((contactAngleData.before - contactAngleData.after) / contactAngleData.before * 100);

  return (
    <div className="contact-angle-comparison">
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {/* 可视化演示 */}
        <div className="canvas-card">
          <div className="card-title">水滴角测试对比</div>
          <div style={{ textAlign: 'center' }}>
            <canvas
              ref={canvasRef}
              style={{
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '4px',
                maxWidth: '100%',
                backgroundColor: 'rgba(255, 255, 255, 0.05)'
              }}
            />
          </div>
        </div>

        {/* 数据统计 */}
        <div className="statistics-card">
          <div className="card-title">性能改善数据</div>
          <div className="statistics-grid">
            <Statistic
              title={<span style={{ color: '#b0b0b0' }}>处理前接触角</span>}
              value={contactAngleData.before}
              suffix="°"
              valueStyle={{ color: '#3498db', fontSize: '16px' }}
            />
            <Statistic
              title={<span style={{ color: '#b0b0b0' }}>处理后接触角</span>}
              value={contactAngleData.after}
              suffix="°"
              valueStyle={{ color: '#2ecc71', fontSize: '16px' }}
            />
            <Statistic
              title={<span style={{ color: '#b0b0b0' }}>改善程度</span>}
              value={improvement.toFixed(1)}
              suffix="%"
              valueStyle={{ color: '#e74c3c', fontSize: '16px' }}
            />
          </div>

          <div style={{ marginTop: '16px' }}>
            <Text strong style={{ fontSize: '12px', color: '#ffffff' }}>润湿性改善:</Text>
            <Progress
              percent={improvement}
              strokeColor={{
                '0%': '#ff7875',
                '50%': '#ffa940',
                '100%': '#73d13d'
              }}
              size="small"
              style={{ marginTop: '8px' }}
            />
          </div>
        </div>

        {/* 表面性质变化 */}
        <div className="properties-card">
          <div className="card-title">表面性质变化</div>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <div className="property-change">
              <Text strong style={{ fontSize: '11px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>表面能变化:</Text>
              <div style={{ marginTop: '4px' }}>
                <Text style={{ fontSize: '10px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
                  32 → 68 mN/m
                </Text>
                <Text type="success" style={{ fontSize: '10px', marginLeft: '8px', color: '#52c41a', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
                  (+36.0)
                </Text>
              </div>
            </div>

            <div className="property-change">
              <Text strong style={{ fontSize: '11px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>表面粗糙度:</Text>
              <div style={{ marginTop: '4px' }}>
                <Text style={{ fontSize: '10px', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
                  {currentMaterial?.roughness || 0.1} → {((currentMaterial?.roughness || 0.1) * 3).toFixed(1)} μm
                </Text>
                <Text type="warning" style={{ fontSize: '10px', marginLeft: '8px', color: '#faad14', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
                  (+{(((currentMaterial?.roughness || 0.1) * 3) - (currentMaterial?.roughness || 0.1)).toFixed(1)})
                </Text>
              </div>
            </div>
          </Space>
        </div>

        {/* 应用效果说明 */}
        <div className="effects-card">
          <div className="card-title">实际应用效果</div>
          <Space direction="vertical" size="small">
            <div className="effect-item">
              <Text strong style={{ fontSize: '11px', color: '#52c41a', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>✓ 粘接强度提升</Text>
              <Text style={{ fontSize: '10px', display: 'block', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
                表面能增加，提高与胶粘剂的结合力
              </Text>
            </div>
            <div className="effect-item">
              <Text strong style={{ fontSize: '11px', color: '#52c41a', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>✓ 涂层附着力改善</Text>
              <Text style={{ fontSize: '10px', display: 'block', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
                表面活性增强，涂层更加牢固
              </Text>
            </div>
            <div className="effect-item">
              <Text strong style={{ fontSize: '11px', color: '#52c41a', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>✓ 清洁度提高</Text>
              <Text style={{ fontSize: '10px', display: 'block', color: '#d0d0d0', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
                去除表面污染物和氧化层
              </Text>
            </div>
          </Space>
        </div>
      </Space>

      <style jsx>{`
        .contact-angle-comparison {
          width: 100%;
          max-height: 500px;
          overflow-y: auto;
          padding-right: 4px;
          scrollbar-width: thin;
          scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }

        .contact-angle-comparison::-webkit-scrollbar {
          width: 6px;
        }

        .contact-angle-comparison::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.05);
          border-radius: 3px;
        }

        .contact-angle-comparison::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
        }

        .contact-angle-comparison::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.3);
        }

        .statistics-grid {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 8px;
          margin-bottom: 12px;
        }

        .property-change {
          padding: 8px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 6px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          transition: all 0.2s ease;
        }

        .property-change:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
        }

        .canvas-card,
        .statistics-card,
        .properties-card,
        .effects-card {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          padding: 12px;
          margin-bottom: 12px;
        }

        .card-title {
          color: #ffffff;
          font-size: 12px;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .effect-item {
          padding: 8px;
          border-left: 3px solid #52c41a;
          background: rgba(82, 196, 26, 0.1);
          margin: 6px 0;
          border-radius: 0 4px 4px 0;
          transition: all 0.2s ease;
          border: 1px solid rgba(82, 196, 26, 0.2);
        }

        .effect-item:hover {
          background: rgba(82, 196, 26, 0.15);
          transform: translateX(2px);
          border-color: rgba(82, 196, 26, 0.3);
        }

        @media (max-width: 768px) {
          .contact-angle-comparison {
            max-height: 400px;
          }

          .statistics-grid {
            grid-template-columns: 1fr;
            gap: 6px;
          }
        }

        @media (max-width: 480px) {
          .contact-angle-comparison {
            max-height: 300px;
          }
        }
      `}</style>
    </div>
  );
};

export default ContactAngleComparison;
