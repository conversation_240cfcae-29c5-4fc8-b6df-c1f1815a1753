import React from 'react';
import { But<PERSON>, Space, Typography, Progress } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined, StopOutlined } from '@ant-design/icons';
import { AnimationState } from '@/types/plasma';
import { usePlasmaStore, selectCurrentProcess, selectProcessProgress, selectRemainingTime, selectAnimationState } from '@/stores/plasmaStore';

const { Text } = Typography;

interface ProcessProgressControlsProps {
  isRunning: boolean;
  onPause?: () => void;
  onResume?: () => void;
  onStop?: () => void;
  onRestart?: () => void;
}

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

const ProcessProgressControls: React.FC<ProcessProgressControlsProps> = ({
  isRunning,
  onPause,
  onResume,
  onStop,
  onRestart
}) => {
  const {
    pauseProcess,
    resumeProcess,
    stopProcess,
    startProcess
  } = usePlasmaStore();

  const currentProcess = usePlasmaStore(selectCurrentProcess);
  const processProgress = usePlasmaStore(selectProcessProgress);
  const remainingTime = usePlasmaStore(selectRemainingTime);
  const animationState = usePlasmaStore(selectAnimationState);

  const handlePause = () => {
    pauseProcess();
    onPause?.();
  };

  const handleResume = () => {
    resumeProcess();
    onResume?.();
  };

  const handleStop = () => {
    stopProcess();
    onStop?.();
  };

  const handleRestart = () => {
    if (currentProcess) {
      stopProcess();
      setTimeout(() => {
        startProcess();
        onRestart?.();
      }, 100);
    }
  };

  // 如果没有选择工艺或没有运行，不显示控制面板
  if (!currentProcess || (!isRunning && animationState === AnimationState.IDLE)) {
    return null;
  }

  return (
    <div>
      {/* 浮动进度控制面板 - 绝对定位在3D场景右上角 */}
      {(isRunning || animationState === AnimationState.PAUSED || animationState === AnimationState.COMPLETED) && (
        <div className="floating-progress-controls">
          <div className="vertical-layout">
            {/* 上半部分：工艺信息、进度条、时间 */}
            <div className="top-section">
              {/* 工艺信息 */}
              <div className="process-info">
              <Text strong style={{ fontSize: '12px', color: '#ffffff', textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>
                {currentProcess.displayName}
              </Text>
              <Text style={{ fontSize: '10px', color: '#b0b0b0', marginTop: '1px', display: 'block' }}>
                {animationState === AnimationState.COMPLETED ? '完成' :
                 animationState === AnimationState.PAUSED ? '暂停' :
                 '进行中'}
              </Text>
            </div>

            {/* 进度条 */}
            <div className="progress-bar">
              <Progress
                percent={Math.round(processProgress)}
                size="small"
                status={
                  animationState === AnimationState.COMPLETED ? 'success' :
                  animationState === AnimationState.PAUSED ? 'normal' : 'active'
                }
                strokeColor="#9C27B0"
                trailColor="rgba(255, 255, 255, 0.15)"
                showInfo={false}
                strokeWidth={3}
              />
            </div>

            {/* 时间显示 */}
            <div className="time-display">
              <Text style={{ fontSize: '10px', color: '#b0b0b0', whiteSpace: 'nowrap' }}>
                {formatTime(remainingTime)}
              </Text>
            </div>
            </div>

            {/* 下半部分：控制按钮 */}
            <div className="action-buttons">
              <Space size={4} wrap>
                {animationState === AnimationState.RUNNING && (
                  <>
                    <Button
                      icon={<PauseCircleOutlined />}
                      onClick={handlePause}
                      size="small"
                      style={{
                        backgroundColor: 'rgba(255, 193, 7, 0.2)',
                        borderColor: 'rgba(255, 193, 7, 0.4)',
                        color: '#FFC107',
                        minWidth: '38px',
                        fontSize: '9px',
                        height: '26px',
                        padding: '0 4px'
                      }}
                    >
                      暂停
                    </Button>
                    <Button
                      icon={<StopOutlined />}
                      onClick={handleStop}
                      size="small"
                      danger
                      style={{
                        backgroundColor: 'rgba(244, 67, 54, 0.2)',
                        borderColor: 'rgba(244, 67, 54, 0.4)',
                        minWidth: '38px',
                        fontSize: '9px',
                        height: '26px',
                        padding: '0 4px'
                      }}
                    >
                      停止
                    </Button>
                  </>
                )}

                {animationState === AnimationState.PAUSED && (
                  <>
                    <Button
                      type="primary"
                      icon={<PlayCircleOutlined />}
                      onClick={handleResume}
                      size="small"
                      style={{
                        backgroundColor: '#9C27B0',
                        borderColor: '#9C27B0',
                        minWidth: '38px',
                        fontSize: '9px',
                        height: '26px',
                        padding: '0 4px'
                      }}
                    >
                      继续
                    </Button>
                    <Button
                      icon={<StopOutlined />}
                      onClick={handleStop}
                      size="small"
                      danger
                      style={{
                        backgroundColor: 'rgba(244, 67, 54, 0.2)',
                        borderColor: 'rgba(244, 67, 54, 0.4)',
                        minWidth: '38px',
                        fontSize: '9px',
                        height: '26px',
                        padding: '0 4px'
                      }}
                    >
                      停止
                    </Button>
                  </>
                )}

                {animationState === AnimationState.COMPLETED && (
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleRestart}
                    size="small"
                    style={{
                      backgroundColor: '#4CAF50',
                      borderColor: '#4CAF50',
                      minWidth: '50px',
                      fontSize: '9px',
                      height: '26px',
                      padding: '0 6px'
                    }}
                  >
                    重新开始
                  </Button>
                )}
              </Space>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .floating-progress-controls {
          position: absolute;
          top: 20px;
          right: 10px;
          background: rgba(0, 0, 0, 0.25);
          backdrop-filter: blur(6px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          padding: 4px 6px;
          box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
          z-index: 40;
          min-width: 140px;
          max-width: 160px;
          pointer-events: auto;
          transition: all 0.3s ease;
        }

        .vertical-layout {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .top-section {
          display: grid;
          grid-template-columns: auto 1fr auto;
          grid-template-areas: "info progress time";
          align-items: center;
          gap: 6px;
        }

        .process-info {
          grid-area: info;
          min-width: 45px;
        }

        .progress-bar {
          grid-area: progress;
          min-width: 40px;
          z-index: 1;
          position: relative;
        }

        .time-display {
          grid-area: time;
          text-align: right;
          min-width: 30px;
        }

        .action-buttons {
          display: flex;
          justify-content: center;
          align-items: center;
          padding-top: 4px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          margin-top: 4px;
        }

        /* 悬浮效果 */
        .floating-progress-controls:hover {
          background: rgba(0, 0, 0, 0.5);
          border-color: rgba(255, 255, 255, 0.2);
          box-shadow: 0 2px 16px rgba(0, 0, 0, 0.3);
          transform: translateY(-1px);
          min-width: 160px;
          max-width: 180px;
          transition: all 0.3s ease;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .floating-progress-controls {
            top: 16px;
            right: 8px;
            min-width: 90px;
            max-width: 110px;
            padding: 4px 6px;
          }

          .floating-progress-controls:hover {
            min-width: 160px;
            max-width: 180px;
          }

          .compact-layout {
            gap: 3px;
          }

          .action-buttons {
            max-width: 50px;
          }
        }

        @media (max-width: 480px) {
          .floating-progress-controls {
            top: 12px;
            right: 6px;
            min-width: 80px;
            max-width: 100px;
            padding: 3px 4px;
          }

          .floating-progress-controls:hover {
            min-width: 140px;
            max-width: 160px;
          }

          .compact-layout {
            gap: 3px;
          }

          .process-info {
            min-width: 35px;
          }

          .progress-bar {
            min-width: 45px;
          }

          .time-display {
            min-width: 22px;
          }

          .action-buttons {
            max-width: 70px;
          }
        }

        @media (max-width: 360px) {
          .floating-progress-controls {
            min-width: 200px;
            max-width: 220px;
            padding: 6px;
          }

          .compact-layout {
            grid-template-columns: auto 1fr auto;
            grid-template-areas:
              "info progress buttons"
              "time time time";
            gap: 4px;
          }

          .time-display {
            grid-area: time;
            text-align: center;
            margin-top: 2px;
            padding-top: 2px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 9px;
          }
        }

        /* 确保指针事件正常工作 */
        .floating-progress-controls * {
          pointer-events: auto;
        }
      `}</style>
    </div>
  );
};

export default ProcessProgressControls;
