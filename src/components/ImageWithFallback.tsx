'use client';

import React, { useState } from 'react';
import Image from 'next/image';

interface ImageWithFallbackProps {
  src: string;
  alt: string;
  fill?: boolean;
  width?: number;
  height?: number;
  className?: string;
  fallbackType?: 'product' | 'news' | 'default';
  priority?: boolean;
}

const ImageWithFallback: React.FC<ImageWithFallbackProps> = ({
  src,
  alt,
  fill = false,
  width,
  height,
  className = '',
  fallbackType = 'default',
  priority = false
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const getFallbackIcon = () => {
    switch (fallbackType) {
      case 'product':
        return (
          <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        );
      case 'news':
        return (
          <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      default:
        return (
          <svg className="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
    }
  };

  const getFallbackText = () => {
    switch (fallbackType) {
      case 'product':
        return '产品图片';
      case 'news':
        return '新闻图片';
      default:
        return '图片';
    }
  };

  // 如果图片加载失败或没有src，显示占位符
  if (!src || imageError) {
    return (
      <div className={`flex flex-col items-center justify-center text-gray-400 bg-gray-100 ${fill ? 'absolute inset-0' : ''} ${className}`}>
        {getFallbackIcon()}
        <span className="text-sm font-medium">{getFallbackText()}</span>
        {/* 可选：显示占位图片 */}
        <div className="mt-2 text-xs text-gray-300">
          图片加载失败
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${fill ? 'w-full h-full' : ''}`}>
      {/* 加载状态 */}
      {isLoading && (
        <div className={`flex items-center justify-center bg-gray-100 text-gray-400 ${fill ? 'absolute inset-0' : ''} ${className}`}>
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400 mb-2"></div>
            <span className="text-sm">加载中...</span>
          </div>
        </div>
      )}
      
      {/* 实际图片 */}
      <Image
        src={src}
        alt={alt}
        fill={fill}
        width={!fill ? width : undefined}
        height={!fill ? height : undefined}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        priority={priority}
        unoptimized={process.env.NODE_ENV === 'development'} // 开发环境下不优化图片
      />
    </div>
  );
};

export default ImageWithFallback;
