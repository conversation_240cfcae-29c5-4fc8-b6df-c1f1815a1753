'use client';

import { useEffect, useState, useRef } from 'react';

interface ClientInteractionsProps {
  children: React.ReactNode;
}

export default function ClientInteractions({ children }: ClientInteractionsProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [showQRCode, setShowQRCode] = useState(false);
  const sectionsRef = useRef<(HTMLElement | null)[]>([]);

  useEffect(() => {
    setIsLoaded(true);

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      // Calculate scroll progress
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = Math.min(currentScrollY / documentHeight, 1);
      setScrollProgress(progress);

      // Animate sections on scroll
      sectionsRef.current.forEach((section) => {
        if (section) {
          const rect = section.getBoundingClientRect();
          const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

          if (isVisible) {
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
          }
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleWeChatClick = () => {
    setShowQRCode(!showQRCode);
  };

  return (
    <div className={`transition-opacity duration-1000 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
      {/* Scroll Progress Bar */}
      <div
        className="fixed top-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-green-500 z-50 transition-all duration-300"
        style={{ width: `${scrollProgress * 100}%` }}
      />

      {/* QR Code Modal */}
      {showQRCode && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={() => setShowQRCode(false)}
        >
          <div className="bg-white p-8 rounded-lg shadow-xl max-w-sm mx-4">
            <h3 className="text-xl font-bold text-center mb-4">微信联系</h3>
            <div className="w-48 h-48 bg-gray-200 mx-auto mb-4 flex items-center justify-center">
              <span className="text-gray-500">微信二维码</span>
            </div>
            <p className="text-center text-gray-600 mb-4">
              微信号：wxid_gz1xribynia322
            </p>
            <button
              onClick={() => setShowQRCode(false)}
              className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition-colors"
            >
              关闭
            </button>
          </div>
        </div>
      )}

      {/* Pass scroll state and handlers to children */}
      <div
        data-scroll-y={scrollY}
        data-show-qr={showQRCode}
        data-handle-wechat={handleWeChatClick}
      >
        {children}
      </div>
    </div>
  );
}
