import { ReactNode } from 'react';
import Header from './Header';
import Footer from './Footer';

interface LayoutProps {
  children: ReactNode;
  hideBanner?: boolean;
  hideFooter?: boolean;
}

const Layout = ({ children, hideBanner = false, hideFooter = false }: LayoutProps) => {
  return (
    <div className="min-h-screen flex flex-col">
      {!hideBanner && <Header />}
      <main className={hideBanner ? "min-h-screen" : "flex-grow"}>
        {children}
      </main>
      {!hideFooter && <Footer />}
    </div>
  );
};

export default Layout;
