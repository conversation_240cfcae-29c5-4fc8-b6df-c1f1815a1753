import { create } from 'zustand';
import {
  DemoState,
  MaterialType,
  ProcessType,
  AnimationState,
  SceneState
} from '@/types/plasma';
import {
  MATERIALS,
  PROCESSES,
  CONTACT_ANGLE_DATA,
  DEMO_CONFIG
} from '@/constants/plasma';

const initialSceneState: SceneState = {
  selectedMaterial: MaterialType.POLYMER,
  selectedProcess: null,
  animationState: AnimationState.IDLE,
  showParameters: false,
  showComparison: false,
  isProcessRunning: false,
  processProgress: 0,
  remainingTime: 0,
  totalDuration: 0,
  processStartTime: null
};

const initialState: DemoState = {
  scene: initialSceneState,
  materials: MATERIALS,
  processes: PROCESSES,
  contactAngles: CONTACT_ANGLE_DATA,
  realTimeParams: {
    power: 0,
    gasFlow: 0,
    speed: 0,
    timestamp: Date.now()
  },
  config: DEMO_CONFIG,
  isLoading: false,
  error: null
};

// 选择器函数
export const selectCurrentMaterial = (state: any) =>
  state.materials.find((m: any) => m.id === state.scene.selectedMaterial);

export const selectCurrentProcess = (state: any) =>
  state.scene.selectedProcess ? state.processes.find((p: any) => p.id === state.scene.selectedProcess) : null;

export const selectIsProcessRunning = (state: any) =>
  state.scene.isProcessRunning;

export const selectContactAngleData = (state: any) =>
  state.contactAngles.find((data: any) => data.material === state.scene.selectedMaterial);

export const selectProcessProgress = (state: any) =>
  state.scene.processProgress;

export const selectRemainingTime = (state: any) =>
  state.scene.remainingTime;

export const selectAnimationState = (state: any) =>
  state.scene.animationState;

// Store interface
interface PlasmaStore extends DemoState {
  setSelectedMaterial: (material: MaterialType, autoStart?: boolean) => void;
  setSelectedProcess: (process: ProcessType | null, autoStop?: boolean, autoStart?: boolean) => void;
  setAnimationState: (state: AnimationState) => void;
  toggleParameters: () => void;
  toggleComparison: () => void;
  startProcess: () => void;
  stopProcess: () => void;
  pauseProcess: () => void;
  resumeProcess: () => void;
  resetScene: () => void;
  updateProgress: (progress: number, remainingTime: number) => void;
  completeProcess: () => void;
  // 内部定时器管理
  _timerId: NodeJS.Timeout | null;
  _clearTimer: () => void;
}

export const usePlasmaStore = create<PlasmaStore>((set, get) => ({
  ...initialState,
  _timerId: null,

  _clearTimer: () => {
    const state = get();
    if (state._timerId) {
      clearInterval(state._timerId);
      set({ _timerId: null });
    }
  },

  setSelectedMaterial: (material: MaterialType, autoStart = false) => {
    const state = get();

    // 如果当前有进程在运行，先停止
    if (state.scene.isProcessRunning) {
      state.stopProcess();
    }

    set((state) => ({
      scene: {
        ...state.scene,
        selectedMaterial: material
      }
    }));

    // 如果有选中的工艺且需要自动开始，则自动开始处理
    if (autoStart && state.scene.selectedProcess) {
      setTimeout(() => {
        const currentState = get();
        currentState.startProcess();
      }, 100); // 短暂延迟确保状态更新完成
    }
  },

  setSelectedProcess: (process: ProcessType | null, autoStop = true, autoStart = false) => {
    const state = get();

    // 如果当前有进程在运行且autoStop为true，先停止当前进程
    if (autoStop && state.scene.isProcessRunning) {
      state.stopProcess();
    }

    // 获取新工艺的持续时间
    let totalDuration = 0;
    if (process) {
      const processData = state.processes.find(p => p.id === process);
      totalDuration = processData?.duration || 30;
    }

    set((state) => ({
      scene: {
        ...state.scene,
        selectedProcess: process,
        processProgress: 0,
        remainingTime: totalDuration,
        totalDuration: totalDuration,
        animationState: AnimationState.IDLE,
        isProcessRunning: false,
        processStartTime: null
      }
    }));

    // 如果需要自动开始且有选中的工艺，则自动开始处理
    if (autoStart && process) {
      setTimeout(() => {
        const currentState = get();
        currentState.startProcess();
      }, 100); // 短暂延迟确保状态更新完成
    }
  },

  setAnimationState: (animationState: AnimationState) => {
    set((state) => ({
      scene: {
        ...state.scene,
        animationState
      }
    }));
  },

  updateProgress: (progress: number, remainingTime: number) => {
    set((state) => ({
      scene: {
        ...state.scene,
        processProgress: Math.min(100, Math.max(0, progress)),
        remainingTime: Math.max(0, remainingTime)
      }
    }));
  },

  toggleParameters: () => {
    set((state) => ({
      scene: {
        ...state.scene,
        showParameters: !state.scene.showParameters
      }
    }));
  },

  toggleComparison: () => {
    set((state) => ({
      scene: {
        ...state.scene,
        showComparison: !state.scene.showComparison
      }
    }));
  },

  startProcess: () => {
    const state = get();

    // 清除之前的定时器
    state._clearTimer();

    if (!state.scene.selectedProcess) return;

    const startTime = Date.now();
    const totalDuration = state.scene.totalDuration;

    set((state) => ({
      scene: {
        ...state.scene,
        animationState: AnimationState.RUNNING,
        isProcessRunning: true,
        processStartTime: startTime,
        processProgress: 0,
        remainingTime: totalDuration
      }
    }));

    // 启动进度更新定时器
    const timerId = setInterval(() => {
      const currentState = get();
      if (!currentState.scene.isProcessRunning) {
        clearInterval(timerId);
        return;
      }

      const elapsed = (Date.now() - startTime) / 1000; // 秒
      const progress = Math.min(100, (elapsed / totalDuration) * 100);
      const remaining = Math.max(0, totalDuration - elapsed);

      if (progress >= 100) {
        // 处理完成
        clearInterval(timerId);
        currentState.completeProcess();
      } else {
        currentState.updateProgress(progress, remaining);
      }
    }, 100); // 每100ms更新一次

    set({ _timerId: timerId });
  },

  stopProcess: () => {
    const state = get();
    state._clearTimer();

    set((state) => ({
      scene: {
        ...state.scene,
        animationState: AnimationState.IDLE,
        isProcessRunning: false,
        processProgress: 0,
        remainingTime: state.scene.totalDuration,
        processStartTime: null
      }
    }));
  },

  pauseProcess: () => {
    const state = get();
    if (state.scene.animationState === AnimationState.RUNNING) {
      state._clearTimer();
      set((state) => ({
        scene: {
          ...state.scene,
          animationState: AnimationState.PAUSED,
          isProcessRunning: false
        }
      }));
    }
  },

  resumeProcess: () => {
    const state = get();
    if (state.scene.animationState === AnimationState.PAUSED) {
      // 重新计算开始时间，考虑已经过去的时间
      const elapsedProgress = state.scene.processProgress / 100;
      const elapsedTime = elapsedProgress * state.scene.totalDuration;
      const newStartTime = Date.now() - (elapsedTime * 1000);

      set((state) => ({
        scene: {
          ...state.scene,
          animationState: AnimationState.RUNNING,
          isProcessRunning: true,
          processStartTime: newStartTime
        }
      }));

      // 重新启动定时器
      state.startProcess();
    }
  },

  completeProcess: () => {
    const state = get();
    state._clearTimer();

    set((state) => ({
      scene: {
        ...state.scene,
        animationState: AnimationState.COMPLETED,
        isProcessRunning: false,
        processProgress: 100,
        remainingTime: 0
      }
    }));
  },

  resetScene: () => {
    const state = get();
    state._clearTimer();

    set(() => ({
      scene: initialSceneState,
      _timerId: null
    }));
  }
}));
