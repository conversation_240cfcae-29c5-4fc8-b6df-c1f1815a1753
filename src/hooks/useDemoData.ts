import { useState, useEffect, useCallback } from 'react';
import { Material, Process, MaterialType, ProcessType } from '@/types/plasma';
import { demoApi, cachedApiRequest } from '@/lib/api/demo';

// 加载状态类型
interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

// 材料数据Hook
export function useMaterials() {
  const [materials, setMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: true,
    error: null,
  });

  const fetchMaterials = useCallback(async () => {
    try {
      setLoading({ isLoading: true, error: null });
      const data = await cachedApiRequest(
        'materials',
        () => demoApi.materials.getAll()
      );
      setMaterials(data);
    } catch (error) {
      setLoading({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load materials',
      });
    } finally {
      setLoading(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  useEffect(() => {
    fetchMaterials();
  }, [fetchMaterials]);

  return {
    materials,
    ...loading,
    refetch: fetchMaterials,
  };
}

// 工艺数据Hook
export function useProcesses() {
  const [processes, setProcesses] = useState<Process[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: true,
    error: null,
  });

  const fetchProcesses = useCallback(async () => {
    try {
      setLoading({ isLoading: true, error: null });
      const data = await cachedApiRequest(
        'processes',
        () => demoApi.processes.getAll()
      );
      setProcesses(data);
    } catch (error) {
      setLoading({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load processes',
      });
    } finally {
      setLoading(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  useEffect(() => {
    fetchProcesses();
  }, [fetchProcesses]);

  return {
    processes,
    ...loading,
    refetch: fetchProcesses,
  };
}

// 材料特定工艺参数Hook
export function useMaterialProcess(materialId?: MaterialType, processId?: ProcessType) {
  const [processData, setProcessData] = useState<Process | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    error: null,
  });

  const fetchMaterialProcess = useCallback(async (matId: MaterialType, procId: ProcessType) => {
    try {
      setLoading({ isLoading: true, error: null });
      const cacheKey = `material-process-${matId}-${procId}`;
      const data = await cachedApiRequest(
        cacheKey,
        () => demoApi.processes.getByMaterialAndProcess(matId, procId)
      );
      setProcessData(data);
    } catch (error) {
      setLoading({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load material process data',
      });
    } finally {
      setLoading(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  useEffect(() => {
    if (materialId && processId) {
      fetchMaterialProcess(materialId, processId);
    }
  }, [materialId, processId, fetchMaterialProcess]);

  return {
    processData,
    ...loading,
    refetch: materialId && processId 
      ? () => fetchMaterialProcess(materialId, processId)
      : undefined,
  };
}

// 演示配置Hook
export function useDemoConfig(section?: string) {
  const [config, setConfig] = useState<Record<string, unknown> | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: true,
    error: null,
  });

  const fetchConfig = useCallback(async () => {
    try {
      setLoading({ isLoading: true, error: null });
      const cacheKey = section ? `config-${section}` : 'config';
      const data = await cachedApiRequest(
        cacheKey,
        () => section ? demoApi.config.getSection(section) : demoApi.config.getAll()
      );
      setConfig(data);
    } catch (error) {
      setLoading({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load config',
      });
    } finally {
      setLoading(prev => ({ ...prev, isLoading: false }));
    }
  }, [section]);

  useEffect(() => {
    fetchConfig();
  }, [fetchConfig]);

  return {
    config,
    ...loading,
    refetch: fetchConfig,
  };
}

// 演示产品数据Hook
export function useDemoProducts(type: 'products' | 'news' = 'products', limit?: number) {
  const [data, setData] = useState<Record<string, unknown>[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: true,
    error: null,
  });

  const fetchData = useCallback(async () => {
    try {
      setLoading({ isLoading: true, error: null });
      const cacheKey = `demo-${type}${limit ? `-${limit}` : ''}`;
      const result = await cachedApiRequest(
        cacheKey,
        () => type === 'products' 
          ? demoApi.products.getProducts(limit)
          : demoApi.products.getNews(limit)
      );
      setData(result);
    } catch (error) {
      setLoading({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to load ${type}`,
      });
    } finally {
      setLoading(prev => ({ ...prev, isLoading: false }));
    }
  }, [type, limit]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    ...loading,
    refetch: fetchData,
  };
}

// 组合Hook - 获取所有演示数据
export function useAllDemoData() {
  const materialsResult = useMaterials();
  const processesResult = useProcesses();
  const configResult = useDemoConfig();

  const isLoading = materialsResult.isLoading || 
                   processesResult.isLoading || 
                   configResult.isLoading;

  const error = materialsResult.error || 
                processesResult.error || 
                configResult.error;

  const refetchAll = useCallback(() => {
    materialsResult.refetch();
    processesResult.refetch();
    configResult.refetch();
  }, [materialsResult, processesResult, configResult]);

  return {
    materials: materialsResult.materials,
    processes: processesResult.processes,
    config: configResult.config,
    isLoading,
    error,
    refetch: refetchAll,
  };
}

// 错误重试Hook
export function useRetry(callback: () => void, maxRetries = 3) {
  const [retryCount, setRetryCount] = useState(0);

  const retry = useCallback(() => {
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1);
      setTimeout(callback, 1000 * Math.pow(2, retryCount)); // 指数退避
    }
  }, [callback, retryCount, maxRetries]);

  const reset = useCallback(() => {
    setRetryCount(0);
  }, []);

  return {
    retry,
    reset,
    retryCount,
    canRetry: retryCount < maxRetries,
  };
}
