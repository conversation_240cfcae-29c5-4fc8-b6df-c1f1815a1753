import mysql from 'mysql2/promise';

const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'zengtao123',
  database: 'juli_web',
  charset: 'utf8mb4',
  timezone: '+08:00',
  connectionLimit: 10,
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Test connection function
export async function testConnection() {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    console.log('Database connection successful');
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}

// Generic query function
export async function query<T = Record<string, unknown>>(sql: string, params?: unknown[]): Promise<T[]> {
  try {
    const [rows] = await pool.query(sql, params || []);
    return rows as T[];
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}

// Get single record
export async function queryOne<T = Record<string, unknown>>(sql: string, params?: unknown[]): Promise<T | null> {
  const results = await query<T>(sql, params);
  return results.length > 0 ? results[0] : null;
}

// Insert and return ID
export async function insert(sql: string, params?: unknown[]): Promise<number> {
  try {
    const [result] = await pool.query(sql, params || []);
    return (result as { insertId: number }).insertId;
  } catch (error) {
    console.error('Database insert error:', error);
    throw error;
  }
}

// Update and return affected rows
export async function update(sql: string, params?: unknown[]): Promise<number> {
  try {
    const [result] = await pool.query(sql, params || []);
    return (result as { affectedRows: number }).affectedRows;
  } catch (error) {
    console.error('Database update error:', error);
    throw error;
  }
}

// Delete and return affected rows
export async function deleteRecord(sql: string, params?: unknown[]): Promise<number> {
  try {
    const [result] = await pool.query(sql, params || []);
    return (result as { affectedRows: number }).affectedRows;
  } catch (error) {
    console.error('Database delete error:', error);
    throw error;
  }
}

export default pool;
