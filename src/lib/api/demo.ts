// 演示数据API客户端
import { Material, Process, MaterialType, ProcessType } from '@/types/plasma';

const API_BASE = '/api/demo';

// API响应类型
interface ApiResponse<T> {
  data: T;
  total?: number;
  error?: string;
}

// 错误处理
class ApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'ApiError';
  }
}

// 通用API请求函数
async function apiRequest<T>(url: string, options?: RequestInit): Promise<T> {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new ApiError(
        `API request failed: ${response.statusText}`,
        response.status
      );
    }

    const result: ApiResponse<T> = await response.json();
    
    if (result.error) {
      throw new ApiError(result.error);
    }

    return result.data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError('Network error or invalid response');
  }
}

// 材料数据API
export const materialsApi = {
  // 获取所有材料
  getAll: (): Promise<Material[]> => 
    apiRequest<Material[]>(`${API_BASE}/materials`),

  // 获取单个材料
  getById: (id: MaterialType): Promise<Material> => 
    apiRequest<Material>(`${API_BASE}/materials?id=${id}`),

  // 更新材料数据
  update: (data: Partial<Material>): Promise<{ success: boolean }> =>
    apiRequest(`${API_BASE}/materials`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// 工艺数据API
export const processesApi = {
  // 获取所有工艺
  getAll: (): Promise<Process[]> => 
    apiRequest<Process[]>(`${API_BASE}/processes`),

  // 获取单个工艺
  getById: (id: ProcessType): Promise<Process> => 
    apiRequest<Process>(`${API_BASE}/processes?id=${id}`),

  // 获取材料特定的工艺参数
  getByMaterialAndProcess: (materialId: MaterialType, processId: ProcessType): Promise<Process> =>
    apiRequest<Process>(`${API_BASE}/processes?id=${processId}&material=${materialId}`),

  // 更新工艺数据
  update: (data: Partial<Process>): Promise<{ success: boolean }> =>
    apiRequest(`${API_BASE}/processes`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// 演示配置API
export const configApi = {
  // 获取完整配置
  getAll: (): Promise<Record<string, unknown>> =>
    apiRequest(`${API_BASE}/config`),

  // 获取特定配置节
  getSection: (section: string): Promise<Record<string, unknown>> =>
    apiRequest(`${API_BASE}/config?section=${section}`),

  // 更新配置
  update: (data: Record<string, unknown>): Promise<{ success: boolean }> =>
    apiRequest(`${API_BASE}/config`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// 演示产品数据API
export const demoProductsApi = {
  // 获取产品数据
  getProducts: (limit?: number): Promise<Record<string, unknown>[]> => {
    const url = limit
      ? `${API_BASE}/products?type=products&limit=${limit}`
      : `${API_BASE}/products?type=products`;
    return apiRequest<Record<string, unknown>[]>(url);
  },

  // 获取新闻数据
  getNews: (limit?: number): Promise<Record<string, unknown>[]> => {
    const url = limit
      ? `${API_BASE}/products?type=news&limit=${limit}`
      : `${API_BASE}/products?type=news`;
    return apiRequest<Record<string, unknown>[]>(url);
  },

  // 更新数据
  update: (data: Record<string, unknown>): Promise<{ success: boolean }> =>
    apiRequest(`${API_BASE}/products`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// 缓存管理
class ApiCache {
  private cache = new Map<string, { data: unknown; timestamp: number }>();
  private readonly TTL = 5 * 60 * 1000; // 5分钟缓存

  get<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key);
      return null;
    }

    return cached.data as T;
  }

  set<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  clear(): void {
    this.cache.clear();
  }

  delete(key: string): void {
    this.cache.delete(key);
  }
}

export const apiCache = new ApiCache();

// 带缓存的API请求
export async function cachedApiRequest<T>(
  key: string,
  apiCall: () => Promise<T>,
  useCache = true
): Promise<T> {
  if (useCache) {
    const cached = apiCache.get<T>(key);
    if (cached) {
      return cached;
    }
  }

  try {
    const data = await apiCall();
    if (useCache) {
      apiCache.set(key, data);
    }
    return data;
  } catch (error) {
    console.error(`API request failed for key: ${key}`, error);
    throw error;
  }
}

// 预加载数据
export async function preloadDemoData(): Promise<void> {
  try {
    await Promise.all([
      cachedApiRequest('materials', materialsApi.getAll),
      cachedApiRequest('processes', processesApi.getAll),
      cachedApiRequest('config', configApi.getAll),
    ]);
  } catch (error) {
    console.warn('Failed to preload demo data:', error);
  }
}

// 导出所有API
export const demoApi = {
  materials: materialsApi,
  processes: processesApi,
  config: configApi,
  products: demoProductsApi,
  cache: apiCache,
  preload: preloadDemoData,
};
