// 演示数据数据库模型
import { query, queryOne, insert, update } from '@/lib/db';
import { Material, Process, MaterialType, ProcessType } from '@/types/plasma';

// 数据库表接口定义
interface DbMaterial {
  id: string;
  name: string;
  display_name: string;
  description: string;
  color: string;
  roughness: number;
  metalness: number;
  model_path: string;
  surface_properties: string; // JSON字符串
  applications: string; // JSON字符串
  created_at: Date;
  updated_at: Date;
  is_active: boolean;
}

interface DbProcess {
  id: string;
  name: string;
  display_name: string;
  description: string;
  color: string;
  duration: number;
  parameters: string; // JSON字符串
  effects: string; // JSON字符串
  created_at: Date;
  updated_at: Date;
  is_active: boolean;
}

interface DbConfig {
  id: number;
  config_key: string;
  config_value: string; // JSON字符串
  description: string;
  created_at: Date;
  updated_at: Date;
}

// interface DbProduct {
//   id: number;
//   name: string;
//   model: string;
//   slug: string;
//   category: string;
//   description: string;
//   href: string;
//   image: string;
//   sort_order: number;
//   created_at: Date;
//   updated_at: Date;
//   is_active: boolean;
// }

// interface DbNews {
//   id: number;
//   title: string;
//   excerpt: string;
//   content: string;
//   category: string;
//   href: string;
//   publish_date: Date;
//   sort_order: number;
//   created_at: Date;
//   updated_at: Date;
//   is_active: boolean;
// }

// 材料数据模型
export class DemoMaterialModel {
  // 获取所有材料
  static async getAll(): Promise<Material[]> {
    const sql = `
      SELECT * FROM demo_materials 
      WHERE is_active = true 
      ORDER BY id
    `;
    const rows = await query<DbMaterial>(sql);
    return rows.map(this.transformToMaterial);
  }

  // 根据ID获取材料
  static async getById(id: MaterialType): Promise<Material | null> {
    const sql = `
      SELECT * FROM demo_materials 
      WHERE id = ? AND is_active = true
    `;
    const row = await queryOne<DbMaterial>(sql, [id]);
    return row ? this.transformToMaterial(row) : null;
  }

  // 创建材料
  static async create(material: Omit<Material, 'id'>): Promise<string> {
    const sql = `
      INSERT INTO demo_materials (
        id, name, display_name, description, color, roughness, metalness,
        model_path, surface_properties, applications
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const id = material.name.toLowerCase().replace(/\s+/g, '_');
    const params = [
      id,
      material.name,
      material.displayName,
      material.description,
      material.color,
      material.roughness,
      material.metalness,
      material.modelPath,
      JSON.stringify((material as any).surfaceProperties),
      JSON.stringify((material as any).applications)
    ];

    await insert(sql, params);
    return id;
  }

  // 更新材料
  static async updateById(id: MaterialType, material: Partial<Material>): Promise<boolean> {
    const fields: string[] = [];
    const params: unknown[] = [];

    if (material.displayName) {
      fields.push('display_name = ?');
      params.push(material.displayName);
    }
    if (material.description) {
      fields.push('description = ?');
      params.push(material.description);
    }
    if (material.color) {
      fields.push('color = ?');
      params.push(material.color);
    }
    if (material.roughness !== undefined) {
      fields.push('roughness = ?');
      params.push(material.roughness);
    }
    if (material.metalness !== undefined) {
      fields.push('metalness = ?');
      params.push(material.metalness);
    }
    if (material.modelPath) {
      fields.push('model_path = ?');
      params.push(material.modelPath);
    }
    if ((material as any).surfaceProperties) {
      fields.push('surface_properties = ?');
      params.push(JSON.stringify((material as any).surfaceProperties));
    }
    if ((material as any).applications) {
      fields.push('applications = ?');
      params.push(JSON.stringify((material as any).applications));
    }

    if (fields.length === 0) return false;

    const sql = `
      UPDATE demo_materials 
      SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND is_active = true
    `;
    params.push(id);

    const affectedRows = await update(sql, params);
    return affectedRows > 0;
  }

  // 删除材料
  static async deleteById(id: MaterialType): Promise<boolean> {
    const sql = `
      UPDATE demo_materials 
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    const affectedRows = await update(sql, [id]);
    return affectedRows > 0;
  }

  // 转换数据库记录为Material对象
  private static transformToMaterial(row: DbMaterial): Material {
    return {
      id: row.id as MaterialType,
      name: row.name,
      displayName: row.display_name,
      description: row.description,
      color: row.color,
      roughness: row.roughness,
      metalness: row.metalness,
      modelPath: row.model_path,
      surfaceProperties: typeof row.surface_properties === 'string'
        ? JSON.parse(row.surface_properties)
        : row.surface_properties,
      applications: typeof row.applications === 'string'
        ? JSON.parse(row.applications)
        : row.applications
    };
  }
}

// 工艺数据模型
export class DemoProcessModel {
  // 获取所有工艺
  static async getAll(): Promise<Process[]> {
    const sql = `
      SELECT * FROM demo_processes 
      WHERE is_active = true 
      ORDER BY id
    `;
    const rows = await query<DbProcess>(sql);
    return rows.map(this.transformToProcess);
  }

  // 根据ID获取工艺
  static async getById(id: ProcessType): Promise<Process | null> {
    const sql = `
      SELECT * FROM demo_processes 
      WHERE id = ? AND is_active = true
    `;
    const row = await queryOne<DbProcess>(sql, [id]);
    return row ? this.transformToProcess(row) : null;
  }

  // 获取材料特定的工艺参数
  static async getByMaterialAndProcess(
    materialId: MaterialType, 
    processId: ProcessType
  ): Promise<Process | null> {
    // 首先尝试获取自定义参数
    const customSql = `
      SELECT p.*, mp.custom_parameters, mp.custom_effects
      FROM demo_processes p
      LEFT JOIN demo_material_processes mp ON p.id = mp.process_id AND mp.material_id = ?
      WHERE p.id = ? AND p.is_active = true
    `;
    
    const row = await queryOne<DbProcess & { 
      custom_parameters?: string; 
      custom_effects?: string; 
    }>(customSql, [materialId, processId]);
    
    if (!row) return null;

    const process = this.transformToProcess(row);
    
    // 如果有自定义参数，则覆盖默认参数
    if (row.custom_parameters) {
      process.parameters = { ...process.parameters, ...JSON.parse(row.custom_parameters) };
    }
    if (row.custom_effects) {
      (process as any).effects = JSON.parse(row.custom_effects);
    }

    return process;
  }

  // 转换数据库记录为Process对象
  private static transformToProcess(row: DbProcess): Process {
    return {
      id: row.id as ProcessType,
      name: row.name,
      displayName: row.display_name,
      description: row.description,
      color: row.color,
      duration: row.duration,
      parameters: typeof row.parameters === 'string'
        ? JSON.parse(row.parameters)
        : row.parameters,
      effects: typeof row.effects === 'string'
        ? JSON.parse(row.effects)
        : row.effects
    };
  }
}

// 配置数据模型
export class DemoConfigModel {
  // 获取所有配置
  static async getAll(): Promise<Record<string, unknown>> {
    const sql = `SELECT config_key, config_value FROM demo_configs ORDER BY config_key`;
    const rows = await query<DbConfig>(sql);
    
    const config: Record<string, unknown> = {};
    rows.forEach(row => {
      config[row.config_key] = typeof row.config_value === 'string'
        ? JSON.parse(row.config_value)
        : row.config_value;
    });
    
    return config;
  }

  // 获取特定配置
  static async getByKey(key: string): Promise<unknown | null> {
    const sql = `SELECT config_value FROM demo_configs WHERE config_key = ?`;
    const row = await queryOne<DbConfig>(sql, [key]);
    if (!row) return null;

    return typeof row.config_value === 'string'
      ? JSON.parse(row.config_value)
      : row.config_value;
  }

  // 设置配置
  static async setConfig(key: string, value: unknown, description?: string): Promise<boolean> {
    const sql = `
      INSERT INTO demo_configs (config_key, config_value, description)
      VALUES (?, ?, ?)
      ON DUPLICATE KEY UPDATE 
        config_value = VALUES(config_value),
        description = VALUES(description),
        updated_at = CURRENT_TIMESTAMP
    `;
    
    const params = [key, JSON.stringify(value), description || ''];
    const result = await insert(sql, params);
    return result > 0;
  }
}
