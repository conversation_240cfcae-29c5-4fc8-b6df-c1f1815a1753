'use client';

import Layout from '@/components/Layout';
import { useEffect, useState } from 'react';

export default function Honors() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const honors = [
    {
      title: "ISO 9001质量管理体系认证",
      date: "2023年",
      description: "通过国际标准化组织质量管理体系认证，确保产品质量和服务标准",
      image: "🏆"
    },
    {
      title: "高新技术企业认证",
      date: "2022年",
      description: "获得国家高新技术企业认证，技术创新能力得到权威认可",
      image: "🚀"
    },
    {
      title: "等离子清洗技术专利",
      date: "2021年",
      description: "获得多项等离子清洗技术发明专利，技术水平行业领先",
      image: "📜"
    },
    {
      title: "优秀供应商奖",
      date: "2023年",
      description: "获得多家知名企业颁发的优秀供应商奖项，服务质量广受认可",
      image: "🥇"
    },
    {
      title: "环保认证证书",
      date: "2022年",
      description: "产品通过环保认证，符合绿色制造和环境保护要求",
      image: "🌱"
    },
    {
      title: "技术创新奖",
      date: "2021年",
      description: "在等离子表面处理技术创新方面获得行业技术创新奖",
      image: "💡"
    }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className={`mb-8 transition-all duration-1000 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              <span className="inline-block bg-white/20 text-white px-6 py-3 rounded-full text-lg font-medium backdrop-blur-sm border border-white/30">
                荣誉资质
              </span>
            </div>
            <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-8 transition-all duration-1200 delay-200 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              荣誉资质
            </h1>
            <p className={`text-xl md:text-2xl text-blue-100 leading-relaxed max-w-3xl mx-auto transition-all duration-1400 delay-400 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              专业认证与荣誉见证我们的技术实力和服务品质
            </p>
          </div>
        </div>
      </section>

      {/* Honors Grid */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {honors.map((honor, index) => (
                <div 
                  key={index}
                  className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="text-5xl mb-6 text-center">{honor.image}</div>
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-gray-800 mb-3">{honor.title}</h3>
                    <div className="text-blue-600 font-medium mb-4">{honor.date}</div>
                    <p className="text-gray-600 leading-relaxed">{honor.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Company Strength */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-8">企业实力</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">10+</div>
                <div className="text-gray-600">年行业经验</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">50+</div>
                <div className="text-gray-600">技术专利</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">1000+</div>
                <div className="text-gray-600">客户信赖</div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
