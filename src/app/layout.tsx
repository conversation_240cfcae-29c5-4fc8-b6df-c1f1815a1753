import type { Metada<PERSON> } from "next";
import { GeistSans } from "geist/font/sans";
import { GeistMono } from "geist/font/mono";

import "./globals.css";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* 51la网站统计 - 仅在生产环境加载 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 检查是否为生产环境
              if (typeof window !== 'undefined' &&
                  window.location.hostname !== 'localhost' &&
                  window.location.hostname !== '127.0.0.1') {

                // 动态加载51la统计脚本
                var script = document.createElement('script');
                script.charset = 'UTF-8';
                script.id = 'LA_COLLECT';
                script.src = '//sdk.51.la/js-sdk-pro.min.js';
                script.async = true;

                script.onload = function() {
                  if (typeof LA !== 'undefined') {
                    LA.init({
                      id: "3MxL2unzxXS4DKRH",
                      ck: "3MxL2unzxXS4DKRH",
                      autoTrack: true,
                      screenRecord: true
                    });
                  }
                };

                script.onerror = function() {
                  console.log('51la统计脚本加载失败');
                };

                document.head.appendChild(script);
              }
            `,
          }}
        />
      </head>
      <body
        className={`${GeistSans.variable} ${GeistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
