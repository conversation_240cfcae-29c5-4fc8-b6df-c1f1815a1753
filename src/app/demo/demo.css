/* Enhanced Demo page styles with consistent fullscreen theme */

/* Main demo page container */
.demo-page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  padding: 0;
  margin: 0;
  width: 100vw;
  max-width: none;
}

.demo-content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Demo header section */
.demo-header-section {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.demo-title-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.demo-main-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 1rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.demo-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
}

/* Instructions section */
.demo-instructions-section {
  margin-bottom: 3rem;
}

.instructions-title {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.instructions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.instruction-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.instruction-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.instruction-card-blue {
  border-left: 4px solid #60a5fa;
}

.instruction-card-green {
  border-left: 4px solid #34d399;
}

.instruction-card-purple {
  border-left: 4px solid #a78bfa;
}

.instruction-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 1rem 0;
}

.instruction-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.instruction-list li {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Main demo section */
.demo-main-section {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Enhanced demo wrapper styles */
.demo-main-section .plasma-demo-container {
  background: transparent;
  padding: 0;
  min-height: auto;
}

/* Override Ant Design card styles for consistent theme */
.demo-main-section .ant-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.demo-main-section .ant-card-head {
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px 12px 0 0;
}

.demo-main-section .ant-card-head-title {
  color: #ffffff;
  font-weight: 600;
}

.demo-main-section .ant-card-body {
  color: rgba(255, 255, 255, 0.9);
}

/* Button styling to match fullscreen theme */
.demo-main-section .ant-btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.demo-main-section .ant-btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.demo-main-section .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* Progress bar styling */
.demo-main-section .ant-progress-bg {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

/* Scene container enhanced styling */
.demo-main-section .scene-container {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Ensure 3D canvas takes full container with proper styling */
.demo-main-section .scene-container canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
  border-radius: 12px;
}

/* Loading states */
.demo-main-section .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 2px dashed rgba(255, 255, 255, 0.2);
}

.demo-main-section .loading-text {
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

/* Responsive design */
@media (max-width: 1200px) {
  .demo-content-wrapper {
    max-width: 1200px;
    padding: 1.5rem;
  }

  .demo-main-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .demo-content-wrapper {
    padding: 1rem;
  }

  .demo-main-title {
    font-size: 2rem;
  }

  .demo-subtitle {
    font-size: 1rem;
  }

  .instructions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .demo-title-container {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .demo-main-title {
    font-size: 1.75rem;
  }

  .instruction-card {
    padding: 1rem;
  }
}

/* Animation effects */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.demo-header-section,
.demo-instructions-section,
.demo-main-section {
  animation: fadeInUp 0.6s ease-out;
}

.demo-instructions-section {
  animation-delay: 0.2s;
}

.demo-main-section {
  animation-delay: 0.4s;
}

/* Ensure proper z-index for overlays */
.demo-main-section .ant-modal,
.demo-main-section .ant-drawer {
  z-index: 1050;
}
