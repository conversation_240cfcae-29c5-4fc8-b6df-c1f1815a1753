'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import Breadcrumb from '@/components/Breadcrumb';
import PlasmaDemo3D from '@/components/PlasmaDemo/PlasmaDemo';
import './demo.css';

export default function DemoPage() {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const breadcrumbItems = [
    { label: '首页', href: '/' },
    { label: '3D演示' }
  ];

  // Monitor fullscreen state changes from the PlasmaDemo component
  useEffect(() => {
    const handleFullscreenChange = (event: CustomEvent) => {
      setIsFullscreen(event.detail.isFullscreen);
    };

    window.addEventListener('plasmaFullscreenChange', handleFullscreenChange as EventListener);
    return () => {
      window.removeEventListener('plasmaFullscreenChange', handleFullscreenChange as EventListener);
    };
  }, []);

  // If in fullscreen mode, render only the demo component
  if (isFullscreen) {
    return <PlasmaDemo3D />;
  }

  return (
    <Layout hideBanner={false}>
      <Breadcrumb items={breadcrumbItems} />

      {/* PlasmaDemo Component - Full Width */}
      <div className="demo-page-container">
        <PlasmaDemo3D />
      </div>

      {/* Additional Information */}
      <div className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">了解更多等离子清洗技术</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              通过我们的产品和技术文档，深入了解等离子清洗技术在各行业的应用
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔬</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">产品设备</h3>
              <p className="text-gray-600 mb-4">查看我们的等离子清洗机产品系列</p>
              <a href="/products" className="text-blue-600 hover:text-blue-800 font-medium">
                了解产品 →
              </a>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📚</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">技术文档</h3>
              <p className="text-gray-600 mb-4">深入了解等离子清洗技术原理</p>
              <a href="/news/encyclopedia" className="text-blue-600 hover:text-blue-800 font-medium">
                阅读文档 →
              </a>
            </div>
            
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💼</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">应用案例</h3>
              <p className="text-gray-600 mb-4">查看实际应用案例和解决方案</p>
              <a href="/news/applications" className="text-blue-600 hover:text-blue-800 font-medium">
                查看案例 →
              </a>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
