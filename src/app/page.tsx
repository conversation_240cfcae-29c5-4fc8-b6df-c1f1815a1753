import Layout from '@/components/Layout';
import Link from 'next/link';
import WeChatButton from '@/components/WeChatButton';

export default function Home() {
  return (
    <Layout>

      {/* Hero Banner */}
      <section className="hero-banner relative min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white overflow-hidden flex items-center">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent to-blue-900/30 animate-gradient"></div>

        {/* Floating Particles */}
        <div className="particles-container absolute inset-0">
          <div className="particle particle-1"></div>
          <div className="particle particle-2"></div>
          <div className="particle particle-3"></div>
          <div className="particle particle-4"></div>
          <div className="particle particle-5"></div>
          <div className="particle particle-6"></div>
        </div>

        <div className="relative container mx-auto px-4 py-20 z-10">
          <div className="max-w-4xl mx-auto text-center">
            <div className="mb-8 transition-all duration-1000 animate-fadeInUp">
              <span className="hero-badge inline-block bg-white/20 text-white px-8 py-4 rounded-full text-lg font-medium backdrop-blur-sm border border-white/30">
                <span className="relative z-10">专业等离子清洗技术咨询服务</span>
              </span>
            </div>
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight transition-all duration-1200 delay-200 animate-fadeInUp">
              <span className="hero-title-main block mb-4">等离子清洗专家</span>
              <span className="hero-title-accent block text-gradient bg-gradient-to-r from-yellow-300 via-orange-300 to-yellow-400 bg-clip-text text-transparent animate-shimmer">
                技术咨询与设备推荐
              </span>
            </h1>
            <p className="text-xl md:text-2xl lg:text-3xl mb-12 text-blue-100 leading-relaxed max-w-3xl mx-auto transition-all duration-1400 delay-400 animate-fadeInUp">
              专业的等离子表面处理技术咨询与设备选型服务
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center transition-all duration-1600 delay-600 animate-fadeInUp">
              <Link
                href="/products"
                className="hero-btn btn-primary text-center group relative overflow-hidden px-8 py-4 text-lg"
              >
                <span className="relative z-10 flex items-center gap-3">
                  <svg className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  设备推荐
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-600 transform scale-x-0 origin-left group-hover:scale-x-100 transition-transform duration-300"></div>
              </Link>
              <Link
                href="/demo"
                className="hero-btn btn-secondary text-center group border-white text-white hover:bg-white hover:text-blue-600 relative overflow-hidden px-8 py-4 text-lg"
              >
                <span className="relative z-10 flex items-center gap-3">
                  <svg className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  3D演示
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-blue-500 transform scale-x-0 origin-left group-hover:scale-x-100 transition-transform duration-300"></div>
              </Link>
            </div>
          </div>
        </div>

        {/* Enhanced Decorative elements */}
        <div className="decorative-element absolute top-20 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-float"></div>
        <div className="decorative-element absolute bottom-20 right-20 w-24 h-24 bg-yellow-300/20 rounded-full blur-lg animate-float" style={{ animationDelay: '1s' }}></div>
        <div className="decorative-element absolute top-1/2 left-10 w-16 h-16 bg-blue-300/20 rounded-full blur-md animate-float" style={{ animationDelay: '2s' }}></div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="scroll-section py-24 bg-white relative overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <span className="inline-block bg-blue-100 text-blue-600 px-6 py-3 rounded-full text-lg font-medium mb-6">
                关于我们
              </span>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 mb-8">
                专业等离子清洗
                <span className="block text-blue-600">技术解决方案</span>
              </h2>
              <p className="text-xl md:text-2xl text-gray-600 leading-relaxed max-w-4xl mx-auto">
                专业的等离子清洗技术顾问，专注于等离子表面处理技术咨询，为客户提供专业的技术指导和设备选型建议
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { icon: "⚡", title: "专业技术", desc: "多年技术经验" },
                { icon: "🏆", title: "可靠服务", desc: "专业咨询保障" },
                { icon: "🛠️", title: "技术支持", desc: "全程指导服务" },
                { icon: "🎯", title: "定制建议", desc: "个性化推荐" }
              ].map((feature, index) => (
                <div
                  key={index}
                  className="feature-card bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 text-center group"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-50 rounded-full -translate-y-1/2 translate-x-1/2 opacity-50"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-indigo-50 rounded-full translate-y-1/2 -translate-x-1/2 opacity-50"></div>
      </section>

      {/* Products Section */}
      <section className="scroll-section py-24 bg-gradient-to-br from-gray-50 to-blue-50 relative">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <span className="inline-block bg-blue-100 text-blue-600 px-6 py-3 rounded-full text-lg font-medium mb-6">
                产品系列
              </span>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 mb-8">
                等离子清洗机设备
              </h2>
              <p className="text-xl md:text-2xl text-gray-600 leading-relaxed max-w-4xl mx-auto">
                专业设计各类等离子清洗机，满足不同行业需求
              </p>
            </div>

            {/* Product Categories */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {[
                {
                  name: '小型真空等离子清洗机',
                  desc: '适用于实验室和小批量生产',
                  icon: '🔬',
                  href: '/products/small-vacuum',
                  gradient: 'from-blue-500 to-cyan-500'
                },
                {
                  name: '大型真空等离子清洗机',
                  desc: '工业级大批量生产设备',
                  icon: '🏭',
                  href: '/products/large-vacuum',
                  gradient: 'from-purple-500 to-pink-500'
                },
                {
                  name: '大气等离子清洗机',
                  desc: '常压下高效表面处理',
                  icon: '⚡',
                  href: '/products/atmospheric',
                  gradient: 'from-green-500 to-teal-500'
                }
              ].map((product, index) => (
                <Link key={index} href={product.href} className="group">
                  <div className="product-card bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 relative overflow-hidden">
                    <div className={`absolute inset-0 bg-gradient-to-br ${product.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}></div>
                    <div className="relative z-10">
                      <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                        {product.icon}
                      </div>
                      <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors">
                        {product.name}
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-6">
                        {product.desc}
                      </p>
                      <div className="flex items-center text-blue-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                        了解详情
                        <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            <div className="text-center">
              <Link
                href="/products"
                className="inline-flex items-center gap-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-4 rounded-full text-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                查看全部产品
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </section>



      {/* Contact Section */}
      <section className="scroll-section py-24 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white relative overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block bg-white/20 text-white px-6 py-3 rounded-full text-lg font-medium mb-8 backdrop-blur-sm">
              联系我们
            </span>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8">
              开启合作之旅
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed mb-16 max-w-3xl mx-auto">
              专业的管理团队、技术团队、维修团队，为您提供完善的售后服务
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {[
                { icon: "👤", label: "联系人", value: "曾先生" },
                { icon: "📱", label: "手机", value: "18954901489" },
                { icon: "💬", label: "微信", value: "wxid_gz1xribynia322" },
                { icon: "📧", label: "邮箱", value: "<EMAIL>" },
                { icon: "📍", label: "地址", value: "南京市江宁区" },
                { icon: "🔥", label: "服务热线", value: "18954901489", highlight: true },
                { icon: "🕒", label: "服务时间", value: "7×24小时" }
              ].map((contact, index) => (
                <div
                  key={index}
                  className={`contact-card bg-white/10 backdrop-blur-sm p-6 rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 ${contact.highlight ? 'ring-2 ring-yellow-300' : ''}`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="text-3xl mb-4">{contact.icon}</div>
                  <div className="text-blue-200 text-sm mb-2">{contact.label}</div>
                  <div className={`font-semibold text-lg ${contact.highlight ? 'text-yellow-300' : 'text-white'}`}>
                    {contact.value}
                  </div>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <WeChatButton />
              <Link
                href="/demo"
                className="inline-flex items-center gap-3 bg-transparent border-2 border-white text-white px-8 py-4 rounded-full text-lg font-medium hover:bg-white hover:text-blue-600 transition-all duration-300 transform hover:scale-105"
              >
                体验3D演示
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </Link>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full translate-y-1/2 -translate-x-1/2"></div>
      </section>

    </Layout>
  );
}
