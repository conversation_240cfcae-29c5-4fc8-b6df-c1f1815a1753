'use client';

import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <div className="max-w-lg w-full text-center">
        {/* 404 Animation */}
        <div className="relative mb-8">
          <div className="text-8xl font-bold text-blue-200 select-none">
            404
          </div>
          <div className="absolute inset-0 text-8xl font-bold text-gradient animate-pulse">
            404
          </div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            页面未找到
          </h1>
          <p className="text-gray-600 text-lg mb-2">
            抱歉，您访问的页面不存在或已被移动。
          </p>
          <p className="text-gray-500">
            请检查URL是否正确，或返回首页继续浏览。
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
          <Link 
            href="/"
            className="btn-primary w-full sm:w-auto justify-center"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            返回首页
          </Link>
          
          <button 
            onClick={() => window.history.back()}
            className="btn-secondary w-full sm:w-auto justify-center"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回上页
          </button>
        </div>

        {/* Quick Links */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            您可能在寻找：
          </h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <Link 
              href="/products" 
              className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
            >
              等离子清洗机设备
            </Link>
            <Link 
              href="/demo" 
              className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
            >
              3D技术演示
            </Link>
            <Link 
              href="/news" 
              className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
            >
              新闻中心
            </Link>
            <Link 
              href="/contact" 
              className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
            >
              联系我们
            </Link>
          </div>
        </div>

        {/* Contact Info */}
        <div className="mt-8 p-6 bg-white rounded-lg shadow-md">
          <h4 className="font-semibold text-gray-800 mb-2">需要帮助？</h4>
          <p className="text-gray-600 text-sm mb-3">
            如果您需要技术支持或有任何疑问，请联系我们：
          </p>
          <div className="text-sm text-gray-700">
            <p>联系人：曾先生</p>
            <p>电话：<a href="tel:17761744292" className="text-blue-600 hover:underline">17761744292</a></p>
            <p>邮箱：<a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a></p>
          </div>
        </div>
      </div>
    </div>
  );
}
