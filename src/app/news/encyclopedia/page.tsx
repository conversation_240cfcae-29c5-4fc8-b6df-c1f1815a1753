'use client';

import Layout from '@/components/Layout';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function Encyclopedia() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const articles = [
    {
      title: "什么是等离子清洗技术？",
      date: "2024-01-15",
      excerpt: "等离子清洗是一种干式清洗工艺，利用等离子体中的活性粒子与材料表面发生物理和化学反应，从而去除表面污染物...",
      category: "基础知识",
      readTime: "5分钟"
    },
    {
      title: "等离子清洗机的工作原理详解",
      date: "2024-01-10",
      excerpt: "等离子清洗机通过射频电源产生高频电场，在真空或大气压条件下激发气体分子形成等离子体，实现表面清洗和改性...",
      category: "技术原理",
      readTime: "8分钟"
    },
    {
      title: "真空等离子与大气等离子的区别",
      date: "2024-01-05",
      excerpt: "真空等离子清洗在低压环境下进行，清洗效果更彻底；大气等离子在常压下操作，处理效率更高，各有优势...",
      category: "技术对比",
      readTime: "6分钟"
    },
    {
      title: "等离子清洗在电子制造中的应用",
      date: "2023-12-28",
      excerpt: "在电子制造过程中，等离子清洗技术广泛应用于PCB板清洗、芯片封装前处理、引线框架清洗等关键工艺环节...",
      category: "应用案例",
      readTime: "7分钟"
    },
    {
      title: "如何选择合适的等离子清洗机？",
      date: "2023-12-20",
      excerpt: "选择等离子清洗机需要考虑处理材料、清洗要求、产能需求、成本预算等多个因素，本文为您详细分析选型要点...",
      category: "选型指南",
      readTime: "10分钟"
    },
    {
      title: "等离子清洗机的维护保养指南",
      date: "2023-12-15",
      excerpt: "正确的维护保养可以延长等离子清洗机的使用寿命，提高设备稳定性。本文介绍日常维护、定期保养的具体方法...",
      category: "维护保养",
      readTime: "6分钟"
    }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 text-white py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className={`mb-8 transition-all duration-1000 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              <span className="inline-block bg-white/20 text-white px-6 py-3 rounded-full text-lg font-medium backdrop-blur-sm border border-white/30">
                知识百科
              </span>
            </div>
            <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-8 transition-all duration-1200 delay-200 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              等离子清洗机百科
            </h1>
            <p className={`text-xl md:text-2xl text-indigo-100 leading-relaxed max-w-3xl mx-auto transition-all duration-1400 delay-400 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              深入了解等离子清洗技术的原理、应用和最佳实践
            </p>
          </div>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {articles.map((article, index) => (
                <div 
                  key={index}
                  className={`bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="mb-4">
                    <span className="inline-block bg-indigo-100 text-indigo-600 px-3 py-1 rounded-full text-sm font-medium">
                      {article.category}
                    </span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3 hover:text-indigo-600 transition-colors">
                    {article.title}
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {article.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>{article.date}</span>
                    <span>{article.readTime}</span>
                  </div>
                  <Link
                    href="#"
                    className="inline-flex items-center text-indigo-600 hover:text-indigo-700 font-medium"
                  >
                    阅读全文
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-indigo-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">需要更多技术支持？</h2>
          <p className="text-xl text-indigo-100 mb-8">我们的技术专家随时为您提供专业咨询</p>
          <Link
            href="/contact"
            className="inline-block bg-white text-indigo-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
          >
            联系技术专家
          </Link>
        </div>
      </section>
    </Layout>
  );
}
