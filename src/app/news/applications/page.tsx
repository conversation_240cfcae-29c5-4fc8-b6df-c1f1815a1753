'use client';

import Layout from '@/components/Layout';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function Applications() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const applications = [
    {
      title: "微流控PDMS芯片键合等离子清洗机应用",
      date: "2025-07-24",
      excerpt: "微流控PDMS芯片键合等离子清洗机是一种用于清洗微流控PDMS芯片的设备，该设备在微流控技术和等离子清洗技术的结合下，为PDMS芯片的键合提供了高效、可靠的解决方案...",
      industry: "生物医学",
      image: "🧬"
    },
    {
      title: "等离子外加工清洗机：高效清洗新选择",
      date: "2025-07-24",
      excerpt: "随着现代工业的不断发展，清洗工艺也得到了越来越多的关注。传统的清洗方法可能存在对环境的污染、清洗效率低下等问题，而等离子外加工清洗机作为一种新兴的清洗技术...",
      industry: "工业制造",
      image: "🏭"
    },
    {
      title: "改善聚丙烯腈PAN润湿性和粘接性",
      date: "2025-07-24",
      excerpt: "随着科学技术的不断发展，材料科学领域也在不断进步。在材料研究中，表面性能的改善一直是研究的重点之一。聚丙烯腈(PAN)作为一种重要的高分子材料，其表面改性具有重要意义...",
      industry: "材料科学",
      image: "🧪"
    },
    {
      title: "等离子除胶处理机使用方法指南",
      date: "2025-07-24",
      excerpt: "等离子除胶处理机使用指南：胶水在工业生产过程中难以去除，导致产品质量下降，甚至无法正常使用。等离子除胶处理机作为一种高效、环保的除胶设备，为解决这一问题提供了理想的解决方案...",
      industry: "电子制造",
      image: "💻"
    },
    {
      title: "汽车零部件等离子清洗应用案例",
      date: "2025-07-20",
      excerpt: "在汽车制造过程中，零部件的表面清洁度直接影响后续的涂装、粘接等工艺质量。等离子清洗技术在汽车零部件处理中展现出独特优势，能够有效去除油污、氧化层等污染物...",
      industry: "汽车制造",
      image: "🚗"
    },
    {
      title: "航空航天材料等离子表面改性",
      date: "2025-07-18",
      excerpt: "航空航天领域对材料性能要求极高，等离子表面改性技术能够在不改变材料本体性能的前提下，显著改善表面特性，提高材料的粘接强度、耐腐蚀性和生物相容性...",
      industry: "航空航天",
      image: "🚀"
    }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 text-white py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className={`mb-8 transition-all duration-1000 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              <span className="inline-block bg-white/20 text-white px-6 py-3 rounded-full text-lg font-medium backdrop-blur-sm border border-white/30">
                应用案例
              </span>
            </div>
            <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-8 transition-all duration-1200 delay-200 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              等离子清洗机应用
            </h1>
            <p className={`text-xl md:text-2xl text-emerald-100 leading-relaxed max-w-3xl mx-auto transition-all duration-1400 delay-400 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              探索等离子清洗技术在各行业的实际应用案例
            </p>
          </div>
        </div>
      </section>

      {/* Applications Grid */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {applications.map((app, index) => (
                <div 
                  key={index}
                  className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="flex items-start gap-6">
                    <div className="text-4xl">{app.image}</div>
                    <div className="flex-1">
                      <div className="mb-3">
                        <span className="inline-block bg-emerald-100 text-emerald-600 px-3 py-1 rounded-full text-sm font-medium">
                          {app.industry}
                        </span>
                      </div>
                      <h3 className="text-xl font-bold text-gray-800 mb-3 hover:text-emerald-600 transition-colors">
                        {app.title}
                      </h3>
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {app.excerpt}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">{app.date}</span>
                        <Link
                          href="#"
                          className="inline-flex items-center text-emerald-600 hover:text-emerald-700 font-medium"
                        >
                          查看详情
                          <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industries Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-800 mb-12">应用行业</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { name: "电子制造", icon: "💻" },
                { name: "汽车工业", icon: "🚗" },
                { name: "航空航天", icon: "🚀" },
                { name: "生物医学", icon: "🧬" },
                { name: "材料科学", icon: "🧪" },
                { name: "纺织印染", icon: "🧵" },
                { name: "光学器件", icon: "🔬" },
                { name: "包装印刷", icon: "📦" }
              ].map((industry, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl mb-2">{industry.icon}</div>
                  <div className="text-gray-700 font-medium">{industry.name}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-emerald-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">需要定制化解决方案？</h2>
          <p className="text-xl text-emerald-100 mb-8">我们的应用工程师将为您提供专业的技术方案</p>
          <Link
            href="/contact"
            className="inline-block bg-white text-emerald-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
          >
            咨询应用方案
          </Link>
        </div>
      </section>
    </Layout>
  );
}
