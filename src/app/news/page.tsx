import Layout from '@/components/Layout';
import Breadcrumb from '@/components/Breadcrumb';
import Link from 'next/link';
import ImageWithFallback from '@/components/ImageWithFallback';
import { query } from '@/lib/db';

interface NewsItem {
  id: number;
  title: string;
  excerpt: string;
  category: string;
  href: string;
  publish_date: string;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

interface Article {
  id: number;
  title: string;
  excerpt: string;
  content: string;
  featured_image: string;
  category: {
    id: string;
    name: string;
    slug: string;
    type: 'article';
    sort_order: number;
    created_at: string;
    updated_at: string;
  };
  slug: string;
  status: 'published';
  publish_date: string;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 在构建时获取新闻数据
async function getNewsData(page: number = 1, limit: number = 4) {
  try {
    const offset = (page - 1) * limit;

    // 查询新闻列表
    const newsData = await query<NewsItem>(
      `SELECT id, title, excerpt, category, href, publish_date, sort_order, created_at, updated_at 
       FROM demo_news 
       WHERE is_active = 1 
       ORDER BY sort_order ASC, publish_date DESC 
       LIMIT ? OFFSET ?`,
      [limit, offset]
    );

    // 查询总数
    const countResult = await query<{ total: number }>(
      `SELECT COUNT(*) as total FROM demo_news WHERE is_active = 1`
    );

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / limit);

    // 转换为Article格式
    const articles: Article[] = newsData.map((news) => ({
      id: news.id,
      title: news.title,
      excerpt: news.excerpt,
      content: '',
      featured_image: '',
      category: {
        id: news.category,
        name: news.category === 'applications' ? '等离子清洗机应用' : '等离子清洗机百科',
        slug: news.category,
        type: 'article' as const,
        sort_order: 0,
        created_at: '',
        updated_at: ''
      },
      slug: news.href?.replace('/news/', '') || `news-${news.id}`,
      status: 'published' as const,
      publish_date: news.publish_date,
      sort_order: news.sort_order,
      created_at: news.created_at,
      updated_at: news.updated_at
    }));

    return {
      articles,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('Error fetching news data:', error);
    return {
      articles: [],
      pagination: {
        page: 1,
        limit: 4,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      }
    };
  }
}

export default async function NewsPage() {
  // 在服务端获取数据
  const { articles, pagination } = await getNewsData(1, 4);

  const breadcrumbItems = [
    { label: '首页', href: '/' },
    { label: '新闻中心' }
  ];

  return (
    <Layout>
      <Breadcrumb items={breadcrumbItems} />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              新闻中心
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
              了解等离子清洗技术的最新动态和应用案例
            </p>
          </div>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {articles.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                  {articles.map((article) => (
                    <article key={article.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                      <div className="h-48 bg-gray-100 relative overflow-hidden border-b border-gray-200">
                        <ImageWithFallback
                          src={article.featured_image || ''}
                          alt={article.title}
                          fill
                          className="object-cover"
                          fallbackType="news"
                        />
                      </div>

                      <div className="p-6">
                        <div className="flex items-center mb-3">
                          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                            {article.category.name}
                          </span>
                          <span className="text-gray-500 text-sm ml-3">
                            {new Date(article.publish_date).toLocaleDateString('zh-CN')}
                          </span>
                        </div>

                        <h2 className="text-xl font-bold text-gray-800 mb-3 line-clamp-2">
                          {article.title}
                        </h2>

                        <p className="text-gray-600 mb-4 line-clamp-3">
                          {article.excerpt}
                        </p>

                        <Link
                          href={`/news/${article.slug}`}
                          className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
                        >
                          阅读更多
                          <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                          </svg>
                        </Link>
                      </div>
                    </article>
                  ))}
                </div>

                {/* Static Pagination Info */}
                {pagination.totalPages > 1 && (
                  <div className="flex justify-center items-center space-x-4">
                    <span className="text-gray-600">
                      第 {pagination.page} 页，共 {pagination.totalPages} 页，总计 {pagination.total} 篇文章
                    </span>

                    <div className="flex space-x-2">
                      {pagination.hasPrev && (
                        <Link
                          href={`/news?page=${pagination.page - 1}`}
                          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                        >
                          上一页
                        </Link>
                      )}

                      {pagination.hasNext && (
                        <Link
                          href={`/news?page=${pagination.page + 1}`}
                          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                        >
                          下一页
                        </Link>
                      )}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="flex justify-center items-center py-20">
                <div className="text-center">
                  <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">暂无新闻内容</h3>
                  <p className="text-gray-500 mb-6">目前还没有发布任何新闻，请稍后再来查看</p>
                  <Link
                    href="/contact-us"
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                  >
                    联系我们获取最新资讯
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>
    </Layout>
  );
}
