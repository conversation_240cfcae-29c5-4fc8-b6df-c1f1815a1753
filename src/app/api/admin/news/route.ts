import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 新闻数据类型
interface News {
  id?: number;
  title: string;
  excerpt?: string;
  content?: string;
  category: string;
  href?: string;
  publish_date: string;
  sort_order?: number;
  is_active?: boolean;
}

// GET - 获取新闻列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const status = searchParams.get('status') || '';

    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params: (string | number)[] = [];

    if (search) {
      whereClause += ' AND (title LIKE ? OR excerpt LIKE ? OR content LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (category) {
      whereClause += ' AND category = ?';
      params.push(category);
    }

    if (status !== '') {
      whereClause += ' AND is_active = ?';
      params.push(status === 'true' ? 1 : 0);
    }

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM demo_news ${whereClause}`;
    const [countResult] = await query(countQuery, params);
    const total = (countResult as { total: number }).total;

    // 获取新闻列表
    const newsQuery = `SELECT * FROM demo_news ${whereClause} ORDER BY sort_order ASC, publish_date DESC LIMIT ? OFFSET ?`;
    const queryParams = [...params, limit, offset];
    const news = await query(newsQuery, queryParams);

    return NextResponse.json({
      success: true,
      data: {
        news,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Error fetching news:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch news' },
      { status: 500 }
    );
  }
}

// 生成URL slug的工具函数
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/-+/g, '-') // 多个连字符合并为一个
    .trim()
    .substring(0, 50); // 限制长度
}

// POST - 创建新新闻
export async function POST(request: NextRequest) {
  try {
    const body: News = await request.json();

    // 验证必填字段
    if (!body.title || !body.category || !body.publish_date) {
      return NextResponse.json(
        { success: false, error: 'Title, category, and publish_date are required' },
        { status: 400 }
      );
    }

    // 自动生成href
    const slug = generateSlug(body.title);
    let finalSlug = slug;
    let counter = 1;

    // 检查slug是否已存在，如果存在则添加数字后缀
    while (true) {
      const finalHref = `/news/${finalSlug}`;
      const existingNews = await query(
        'SELECT id FROM demo_news WHERE href = ?',
        [finalHref]
      );

      if (!Array.isArray(existingNews) || existingNews.length === 0) {
        break;
      }

      finalSlug = `${slug}-${counter}`;
      counter++;
    }

    const finalHref = `/news/${finalSlug}`;

    // 插入新新闻
    const insertQuery = `
      INSERT INTO demo_news (
        title, excerpt, content, category, href, publish_date, sort_order, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await query(insertQuery, [
      body.title,
      body.excerpt || null,
      body.content || null,
      body.category,
      finalHref,
      body.publish_date,
      body.sort_order || 0,
      body.is_active !== false ? 1 : 0
    ]);

    return NextResponse.json({
      success: true,
      data: { id: (result as unknown as { insertId: number }).insertId, ...body, href: finalHref }
    });

  } catch (error) {
    console.error('Error creating news:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create news' },
      { status: 500 }
    );
  }
}

// PUT - 更新新闻
export async function PUT(request: NextRequest) {
  try {
    const body: News & { id: number } = await request.json();

    if (!body.id) {
      return NextResponse.json(
        { success: false, error: 'News ID is required' },
        { status: 400 }
      );
    }

    // 验证必填字段
    if (!body.title || !body.category || !body.publish_date) {
      return NextResponse.json(
        { success: false, error: 'Title, category, and publish_date are required' },
        { status: 400 }
      );
    }

    // 更新新闻
    const updateQuery = `
      UPDATE demo_news SET 
        title = ?, excerpt = ?, content = ?, category = ?, href = ?, 
        publish_date = ?, sort_order = ?, is_active = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await query(updateQuery, [
      body.title,
      body.excerpt || null,
      body.content || null,
      body.category,
      body.href || null,
      body.publish_date,
      body.sort_order || 0,
      body.is_active !== false,
      body.id
    ]);

    return NextResponse.json({
      success: true,
      data: body
    });

  } catch (error) {
    console.error('Error updating news:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update news' },
      { status: 500 }
    );
  }
}

// DELETE - 删除新闻
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'News ID is required' },
        { status: 400 }
      );
    }

    await query('DELETE FROM demo_news WHERE id = ?', [id]);

    return NextResponse.json({
      success: true,
      message: 'News deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting news:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete news' },
      { status: 500 }
    );
  }
}
