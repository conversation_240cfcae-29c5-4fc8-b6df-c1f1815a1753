import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET() {
  try {
    // 简单的查询测试
    const result1 = await query('SELECT COUNT(*) as count FROM demo_products');

    // 带参数的查询测试
    const result2 = await query('SELECT * FROM demo_products LIMIT ?', [5]);

    // 带多个参数的查询测试
    const result3 = await query('SELECT * FROM demo_products LIMIT ? OFFSET ?', [3, 0]);

    return NextResponse.json({
      success: true,
      data: {
        count: result1,
        limited: result2,
        paginated: result3
      }
    });
  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json(
      { success: false, error: 'Database test failed', details: error },
      { status: 500 }
    );
  }
}
