import { NextRequest, NextResponse } from 'next/server';
import { queryOne, update } from '@/lib/db';
import { Article } from '@/types';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    
    const articleQuery = `
      SELECT 
        a.*,
        c.name as category_name,
        c.slug as category_slug
      FROM articles a 
      LEFT JOIN categories c ON a.category_id = c.id 
      WHERE a.slug = ? AND a.status = 'published'
    `;
    
    const article = await queryOne<Article & { category_name: string; category_slug: string }>(
      articleQuery, 
      [slug]
    );
    
    if (!article) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      );
    }
    
    // Increment view count
    await update(
      'UPDATE articles SET views = views + 1 WHERE id = ?',
      [article.id]
    );
    
    // Format article with category info
    const formattedArticle = {
      ...article,
      category: article.category_name ? {
        id: article.category_id!,
        name: article.category_name,
        slug: article.category_slug,
        type: 'article' as const,
        sort_order: 0,
        created_at: '',
        updated_at: ''
      } : undefined
    };
    
    return NextResponse.json(formattedArticle);
  } catch (error) {
    console.error('Error fetching article:', error);
    return NextResponse.json(
      { error: 'Failed to fetch article' },
      { status: 500 }
    );
  }
}
