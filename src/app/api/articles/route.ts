import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { Article, PaginatedResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const status = searchParams.get('status') || 'published';

    const offset = (page - 1) * limit;

    // Build query conditions
    let whereClause = 'WHERE a.status = ?';
    const queryParams: (string | number)[] = [status];

    if (category) {
      whereClause += ' AND c.slug = ?';
      queryParams.push(category);
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM articles a 
      LEFT JOIN categories c ON a.category_id = c.id 
      ${whereClause}
    `;

    const countResult = await query<{ total: number }>(countQuery, queryParams);
    const total = countResult[0]?.total || 0;

    // Get articles with pagination
    const articlesQuery = `
      SELECT 
        a.*,
        c.name as category_name,
        c.slug as category_slug
      FROM articles a 
      LEFT JOIN categories c ON a.category_id = c.id 
      ${whereClause}
      ORDER BY a.published_at DESC, a.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const articles = await query<Article & { category_name: string; category_slug: string }>(
      articlesQuery,
      [...queryParams, limit, offset]
    );

    // Format articles with category info
    const formattedArticles = articles.map(article => ({
      ...article,
      category: article.category_name ? {
        id: article.category_id!,
        name: article.category_name,
        slug: article.category_slug,
        type: 'article' as const,
        sort_order: 0,
        created_at: '',
        updated_at: ''
      } : undefined
    }));

    const totalPages = Math.ceil(total / limit);

    const response: PaginatedResponse<Article> = {
      data: formattedArticles,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching articles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch articles' },
      { status: 500 }
    );
  }
}
