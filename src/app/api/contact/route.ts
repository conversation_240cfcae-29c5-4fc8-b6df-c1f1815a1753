import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { ContactFormData } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      email,
      phone,
      company,
      subject,
      message
    }: ContactFormData = body;

    // Validate required fields
    if (!name || !message) {
      return NextResponse.json(
        { error: '姓名和留言内容为必填项' },
        { status: 400 }
      );
    }

    // Validate email format if provided
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return NextResponse.json(
        { error: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    // Validate phone format if provided
    if (phone && !/^1[3-9]\d{9}$/.test(phone.replace(/\D/g, ''))) {
      return NextResponse.json(
        { error: '手机号格式不正确' },
        { status: 400 }
      );
    }

    // Get client IP and user agent
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Insert contact message
    const insertQuery = `
      INSERT INTO contact_messages (
        name, email, phone, company, subject, message,
        status, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'new', ?, ?, NOW())
    `;

    const [result] = await query(insertQuery, [
      name,
      email || null,
      phone || null,
      company || null,
      subject || null,
      message,
      ip,
      userAgent
    ]);

    const messageId = (result as unknown as { insertId: number }).insertId;

    // Send notification email (optional - implement based on requirements)
    // await sendNotificationEmail({ name, email, phone, company, subject, message });

    return NextResponse.json({
      success: true,
      message: '留言提交成功，我们会尽快与您联系！',
      id: messageId
    }, { status: 201 });

  } catch (error) {
    console.error('Error submitting contact form:', error);
    return NextResponse.json(
      { error: '提交失败，请稍后重试' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');

    const offset = (page - 1) * limit;

    // Build query conditions
    let whereClause = '';
    const queryParams: (string | number)[] = [];

    if (status) {
      whereClause = 'WHERE status = ?';
      queryParams.push(status);
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM contact_messages 
      ${whereClause}
    `;

    const countResult = await query<{ total: number }>(countQuery, queryParams);
    const total = countResult[0]?.total || 0;

    // Get messages with pagination
    const messagesQuery = `
      SELECT * FROM contact_messages 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const messages = await query(
      messagesQuery,
      [...queryParams, limit, offset]
    );

    const totalPages = Math.ceil(total / limit);

    const response = {
      data: messages,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching contact messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    );
  }
}
