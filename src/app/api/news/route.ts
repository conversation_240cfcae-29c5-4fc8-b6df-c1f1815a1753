import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const search = searchParams.get('search');

    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE is_active = 1';
    const params: (string | number)[] = [];

    if (category) {
      whereClause += ' AND category = ?';
      params.push(category);
    }

    if (search) {
      whereClause += ' AND (title LIKE ? OR excerpt LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    // 查询新闻列表
    const rows = await query(
      `SELECT id, title, excerpt, category, href, publish_date, sort_order, created_at, updated_at
       FROM demo_news
       ${whereClause}
       ORDER BY sort_order ASC, publish_date DESC
       LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    // 查询总数
    const countResult = await query<{ total: number }>(
      `SELECT COUNT(*) as total FROM demo_news ${whereClause}`,
      params
    );

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('获取新闻列表失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取新闻列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
