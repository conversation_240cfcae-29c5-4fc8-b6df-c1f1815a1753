import { NextRequest, NextResponse } from 'next/server';
import { ProcessType, MaterialType } from '@/types/plasma';
import { DemoProcessModel } from '@/lib/models/demo';

// 工艺数据 - 从常量文件迁移到API（预留用于fallback）
/*
const PROCESSES_DATA = [
  {
    id: ProcessType.ACTIVATION,
    name: 'activation',
    displayName: '表面活化',
    description: '提高表面能，改善润湿性和粘接性能',
    color: '#9C27B0',
    duration: 30.0,
    parameters: {
      power: 100,
      gasFlow: 50,
      speed: 10,
      gas: 'O₂/Ar',
      pressure: 0.5,
      temperature: 25
    },
    effects: [
      '提高表面能',
      '改善润湿性',
      '增强粘接力'
    ]
  },
  {
    id: ProcessType.ETCHING,
    name: 'etching',
    displayName: '等离子刻蚀',
    description: '去除氧化层和污染物，增加表面粗糙度',
    color: '#FF5722',
    duration: 45.0,
    parameters: {
      power: 150,
      gasFlow: 80,
      speed: 8,
      gas: 'Ar/CF₄',
      pressure: 0.8,
      temperature: 35
    },
    effects: [
      '去除污染物',
      '增加粗糙度',
      '提高附着力'
    ]
  },
  {
    id: ProcessType.COATING,
    name: 'coating',
    displayName: '等离子涂层',
    description: '在表面沉积功能性薄膜层',
    color: '#00BCD4',
    duration: 60.0,
    parameters: {
      power: 120,
      gasFlow: 60,
      speed: 6,
      gas: 'SiH₄/N₂',
      pressure: 1.2,
      temperature: 45
    },
    effects: [
      '沉积薄膜',
      '改善性能',
      '增加功能性'
    ]
  }
];

// 材料特定的工艺参数（预留用于fallback）
const MATERIAL_SPECIFIC_PARAMETERS = {
  [MaterialType.POLYMER]: {
    [ProcessType.ACTIVATION]: {
      power: 80,
      gasFlow: 40,
      speed: 12,
      gas: 'O₂/Ar',
      pressure: 0.3,
      temperature: 20,
      effectIntensity: 0.8,
      animationSpeed: 1.0,
      particleColor: '#4CAF50',
      surfaceChangeRate: 0.7
    },
    [ProcessType.ETCHING]: {
      power: 120,
      gasFlow: 60,
      speed: 10,
      gas: 'O₂/CF₄',
      pressure: 0.6,
      temperature: 30,
      effectIntensity: 0.9,
      animationSpeed: 1.2,
      particleColor: '#FF5722',
      surfaceChangeRate: 0.8
    },
    [ProcessType.COATING]: {
      power: 100,
      gasFlow: 50,
      speed: 8,
      gas: 'SiH₄/N₂',
      pressure: 1.0,
      temperature: 40,
      effectIntensity: 0.7,
      animationSpeed: 0.8,
      particleColor: '#00BCD4',
      surfaceChangeRate: 0.6
    }
  },
  [MaterialType.METAL]: {
    [ProcessType.ACTIVATION]: {
      power: 120,
      gasFlow: 60,
      speed: 8,
      gas: 'Ar/O₂',
      pressure: 0.4,
      temperature: 30,
      effectIntensity: 0.9,
      animationSpeed: 1.1,
      particleColor: '#9E9E9E',
      surfaceChangeRate: 0.8
    },
    [ProcessType.ETCHING]: {
      power: 180,
      gasFlow: 100,
      speed: 6,
      gas: 'Ar/CF₄',
      pressure: 1.0,
      temperature: 40,
      effectIntensity: 1.0,
      animationSpeed: 1.3,
      particleColor: '#FF5722',
      surfaceChangeRate: 0.9
    },
    [ProcessType.COATING]: {
      power: 140,
      gasFlow: 70,
      speed: 5,
      gas: 'SiH₄/N₂',
      pressure: 1.5,
      temperature: 50,
      effectIntensity: 0.8,
      animationSpeed: 0.9,
      particleColor: '#00BCD4',
      surfaceChangeRate: 0.7
    }
  },
  [MaterialType.GLASS_CERAMIC]: {
    [ProcessType.ACTIVATION]: {
      power: 90,
      gasFlow: 45,
      speed: 10,
      gas: 'O₂/Ar',
      pressure: 0.35,
      temperature: 25,
      effectIntensity: 0.85,
      animationSpeed: 1.05,
      particleColor: '#2196F3',
      surfaceChangeRate: 0.75
    },
    [ProcessType.ETCHING]: {
      power: 140,
      gasFlow: 70,
      speed: 7,
      gas: 'CF₄/O₂',
      pressure: 0.7,
      temperature: 35,
      effectIntensity: 0.95,
      animationSpeed: 1.25,
      particleColor: '#FF5722',
      surfaceChangeRate: 0.85
    },
    [ProcessType.COATING]: {
      power: 110,
      gasFlow: 55,
      speed: 7,
      gas: 'SiH₄/N₂',
      pressure: 1.1,
      temperature: 42,
      effectIntensity: 0.75,
      animationSpeed: 0.85,
      particleColor: '#00BCD4',
      surfaceChangeRate: 0.65
    }
  }
};
*/

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const processId = searchParams.get('id') as ProcessType;
    const materialId = searchParams.get('material') as MaterialType;

    // 如果指定了工艺ID，返回单个工艺
    if (processId) {
      let process;

      // 如果同时指定了材料，返回材料特定的参数
      if (materialId) {
        process = await DemoProcessModel.getByMaterialAndProcess(materialId, processId);
      } else {
        process = await DemoProcessModel.getById(processId);
      }

      if (!process) {
        return NextResponse.json(
          { error: 'Process not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({ data: process });
    }

    // 返回所有工艺
    const processes = await DemoProcessModel.getAll();
    return NextResponse.json({
      data: processes,
      total: processes.length
    });

  } catch (error) {
    console.error('Error fetching processes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST() {
  try {
    // 预留接口用于未来的工艺数据更新
    // const body = await request.json();

    return NextResponse.json({
      success: true,
      message: 'Process data updated successfully'
    });

  } catch (error) {
    console.error('Error updating processes:', error);
    return NextResponse.json(
      { error: 'Failed to update process data' },
      { status: 500 }
    );
  }
}
