import { NextRequest, NextResponse } from 'next/server';
import { DemoConfigModel } from '@/lib/models/demo';

// 演示配置数据 - 从常量文件迁移到API（预留用于fallback）
/*
const DEMO_CONFIG = {
  // 动画配置
  animation: {
    durations: {
      materialSwitch: 1000,
      processStart: 800,
      processComplete: 1200,
      particleLife: 2000,
      surfaceChange: 1500
    },
    timings: {
      ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      smooth: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    }
  },

  // 场景配置
  scene: {
    camera: {
      position: { x: 0, y: 5, z: 10 },
      fov: 75,
      near: 0.1,
      far: 1000
    },
    lighting: {
      ambient: { color: '#404040', intensity: 0.4 },
      directional: {
        color: '#ffffff',
        intensity: 1.0,
        position: { x: 10, y: 10, z: 5 }
      },
      point: {
        color: '#9C27B0',
        intensity: 0.8,
        position: { x: 0, y: 3, z: 0 }
      }
    },
    background: '#1a1a2e'
  },

  // 性能配置
  performance: {
    targetFPS: 60,
    maxParticles: 1000,
    enableShadows: true,
    enableAntialiasing: true,
    pixelRatio: 2 // 服务器端固定值，客户端可以动态调整
  },

  // UI配置
  ui: {
    floatingPanels: {
      zIndex: 40,
      backdropBlur: '6px',
      borderRadius: '6px',
      padding: '4px 6px'
    },
    colors: {
      primary: '#9C27B0',
      secondary: '#4CAF50',
      accent: '#FF5722',
      background: 'rgba(0, 0, 0, 0.25)',
      text: '#ffffff',
      textSecondary: '#b0b0b0'
    },
    breakpoints: {
      mobile: 768,
      tablet: 1024,
      desktop: 1200
    }
  },

  // 参数范围
  parameterRanges: {
    power: { min: 50, max: 200, unit: 'W' },
    gasFlow: { min: 20, max: 120, unit: 'sccm' },
    speed: { min: 5, max: 15, unit: 'mm/min' },
    pressure: { min: 0.1, max: 2.0, unit: 'Pa' },
    temperature: { min: 15, max: 60, unit: '°C' }
  },

  // 主题颜色
  themeColors: {
    materials: {
      polymer: '#4CAF50',
      metal: '#9E9E9E',
      glass_ceramic: '#2196F3'
    },
    processes: {
      activation: '#9C27B0',
      etching: '#FF5722',
      coating: '#00BCD4'
    },
    status: {
      running: '#4CAF50',
      paused: '#FFC107',
      completed: '#2196F3',
      error: '#F44336'
    }
  },

  // 键盘快捷键
  keyboardShortcuts: {
    startDemo: 'Space',
    pauseResume: 'P',
    stop: 'S',
    reset: 'R',
    fullscreen: 'F',
    help: 'H',
    nextMaterial: 'ArrowRight',
    prevMaterial: 'ArrowLeft',
    nextProcess: 'ArrowDown',
    prevProcess: 'ArrowUp'
  },

  // 技术说明
  technicalInfo: {
    activation: {
      title: '表面活化',
      description: '通过等离子体处理提高材料表面能，改善润湿性和粘接性能。',
      principle: '等离子体中的活性粒子与材料表面发生化学反应，引入极性基团，增加表面能。',
      applications: ['粘接前处理', '涂装前处理', '印刷前处理', '生物相容性改善']
    },
    etching: {
      title: '刻蚀',
      description: '去除表面污染物和氧化层，增加表面粗糙度，提高附着力。',
      principle: '等离子体中的离子轰击材料表面，物理和化学作用同时进行，去除表面层。',
      applications: ['清洁处理', '粗化处理', '去胶处理', '微加工']
    },
    coating: {
      title: '镀膜',
      description: '在材料表面沉积功能性薄膜，赋予特殊性能。',
      principle: '等离子体激活前驱体分子，在基材表面发生聚合反应形成薄膜。',
      applications: ['防腐涂层', '绝缘涂层', '生物涂层', '光学涂层']
    }
  },

  // 应用领域
  applicationFields: [
    {
      icon: '🚗',
      name: '汽车工业',
      description: '汽车零部件表面处理，提高粘接和涂装质量'
    },
    {
      icon: '📱',
      name: '电子产品',
      description: '电子器件清洁和表面改性，提高可靠性'
    },
    {
      icon: '🏥',
      name: '医疗器械',
      description: '医疗器械表面处理，改善生物相容性'
    },
    {
      icon: '✈️',
      name: '航空航天',
      description: '航空航天材料表面处理，提高性能'
    },
    {
      icon: '📦',
      name: '包装材料',
      description: '包装材料表面改性，提高印刷和粘接性能'
    },
    {
      icon: '🔬',
      name: '科研实验',
      description: '科研实验中的样品预处理和表面改性'
    }
  ]
};
*/

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section');

    // 如果指定了配置节，返回特定配置
    if (section) {
      const config = await DemoConfigModel.getByKey(section);
      if (!config) {
        return NextResponse.json(
          { error: 'Config section not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ data: config });
    }

    // 返回完整配置
    const config = await DemoConfigModel.getAll();
    return NextResponse.json({
      data: config
    });

  } catch (error) {
    console.error('Error fetching demo config:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST() {
  try {
    // 预留接口用于未来的配置更新
    // const body = await request.json();

    return NextResponse.json({
      success: true,
      message: 'Demo config updated successfully'
    });

  } catch (error) {
    console.error('Error updating demo config:', error);
    return NextResponse.json(
      { error: 'Failed to update demo config' },
      { status: 500 }
    );
  }
}
