import { NextRequest, NextResponse } from 'next/server';
import { testConnection, query } from '@/lib/db';

export async function GET() {
  try {
    // 测试数据库连接
    const isConnected = await testConnection();

    if (!isConnected) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // 测试查询演示表
    const tables = [
      'demo_materials',
      'demo_processes',
      'demo_configs',
      'demo_products',
      'demo_news'
    ];

    const results: Record<string, unknown> = {
      connection: 'success',
      tables: {} as Record<string, unknown>
    };

    for (const table of tables) {
      try {
        const [countResult] = await query(`SELECT COUNT(*) as count FROM ${table}`);
        const [sampleResult] = await query(`SELECT * FROM ${table} LIMIT 1`);

        (results.tables as Record<string, unknown>)[table] = {
          count: (countResult as { count: number }).count,
          exists: true,
          sample: sampleResult || null
        };
      } catch (error) {
        (results.tables as Record<string, unknown>)[table] = {
          exists: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    return NextResponse.json({
      success: true,
      data: results
    });

  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json(
      {
        error: 'Database test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    if (action === 'init-tables') {
      // 创建演示表 - 分别执行每个CREATE TABLE语句
      const createTableSQLs = [
        `CREATE TABLE IF NOT EXISTS demo_materials (
          id VARCHAR(50) PRIMARY KEY COMMENT '材料ID',
          name VARCHAR(100) NOT NULL COMMENT '材料名称',
          display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
          description TEXT COMMENT '材料描述',
          color VARCHAR(20) NOT NULL COMMENT '材料颜色',
          roughness DECIMAL(3,2) DEFAULT 0.1 COMMENT '粗糙度',
          metalness DECIMAL(3,2) DEFAULT 0 COMMENT '金属度',
          model_path VARCHAR(255) COMMENT '3D模型路径',
          surface_properties JSON COMMENT '表面性质数据',
          applications JSON COMMENT '应用领域',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='演示材料表'`,

        `CREATE TABLE IF NOT EXISTS demo_processes (
          id VARCHAR(50) PRIMARY KEY COMMENT '工艺ID',
          name VARCHAR(100) NOT NULL COMMENT '工艺名称',
          display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
          description TEXT COMMENT '工艺描述',
          color VARCHAR(20) NOT NULL COMMENT '工艺颜色',
          duration INT DEFAULT 30 COMMENT '处理时长(秒)',
          parameters JSON COMMENT '工艺参数',
          effects JSON COMMENT '工艺效果',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='演示工艺表'`,

        `CREATE TABLE IF NOT EXISTS demo_material_processes (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
          material_id VARCHAR(50) NOT NULL COMMENT '材料ID',
          process_id VARCHAR(50) NOT NULL COMMENT '工艺ID',
          custom_parameters JSON COMMENT '自定义参数',
          custom_effects JSON COMMENT '自定义效果',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          UNIQUE KEY unique_material_process (material_id, process_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='材料工艺关联表'`,

        `CREATE TABLE IF NOT EXISTS demo_configs (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
          config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
          config_value JSON NOT NULL COMMENT '配置值',
          description TEXT COMMENT '配置描述',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='演示配置表'`,

        `CREATE TABLE IF NOT EXISTS demo_products (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
          name VARCHAR(200) NOT NULL COMMENT '产品名称',
          model VARCHAR(100) COMMENT '产品型号',
          slug VARCHAR(100) NOT NULL COMMENT 'URL别名',
          category VARCHAR(50) NOT NULL COMMENT '产品类别',
          description TEXT COMMENT '产品描述',
          href VARCHAR(255) COMMENT '链接地址',
          image VARCHAR(255) COMMENT '产品图片',
          sort_order INT DEFAULT 0 COMMENT '排序',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='演示产品表'`,

        `CREATE TABLE IF NOT EXISTS demo_news (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
          title VARCHAR(500) NOT NULL COMMENT '新闻标题',
          excerpt TEXT COMMENT '新闻摘要',
          content TEXT COMMENT '新闻内容',
          category VARCHAR(50) NOT NULL COMMENT '新闻类别',
          href VARCHAR(255) COMMENT '链接地址',
          publish_date DATE NOT NULL COMMENT '发布日期',
          sort_order INT DEFAULT 0 COMMENT '排序',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='演示新闻表'`
      ];

      // 执行创建表的SQL
      for (const sql of createTableSQLs) {
        await query(sql);
      }

      return NextResponse.json({
        success: true,
        message: 'Demo tables created successfully'
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Database init error:', error);
    return NextResponse.json(
      {
        error: 'Database initialization failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
