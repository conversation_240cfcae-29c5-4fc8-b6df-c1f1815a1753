import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { Category } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // 'article' or 'product'
    const parentId = searchParams.get('parent_id');

    // Build query conditions
    let whereClause = '';
    const queryParams: (string | number | null)[] = [];

    if (type) {
      whereClause = 'WHERE type = ?';
      queryParams.push(type);
    }

    if (parentId !== null) {
      if (whereClause) {
        whereClause += ' AND parent_id = ?';
      } else {
        whereClause = 'WHERE parent_id = ?';
      }
      queryParams.push(parentId || null);
    }

    // Get categories
    const categoriesQuery = `
      SELECT * FROM categories 
      ${whereClause}
      ORDER BY sort_order ASC, name ASC
    `;

    const categories = await query<Category>(categoriesQuery, queryParams);

    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      slug,
      type,
      description,
      parent_id,
      sort_order = 0
    } = body;

    // Validate required fields
    if (!name || !slug || !type) {
      return NextResponse.json(
        { error: 'Name, slug, and type are required' },
        { status: 400 }
      );
    }

    // Validate type
    if (!['article', 'product'].includes(type)) {
      return NextResponse.json(
        { error: 'Type must be either "article" or "product"' },
        { status: 400 }
      );
    }

    // Check if slug already exists
    const existingCategory = await query(
      'SELECT id FROM categories WHERE slug = ?',
      [slug]
    );

    if (existingCategory.length > 0) {
      return NextResponse.json(
        { error: 'Category with this slug already exists' },
        { status: 409 }
      );
    }

    // Insert new category
    const insertQuery = `
      INSERT INTO categories (
        name, slug, type, description, parent_id, sort_order, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;

    const [result] = await query(insertQuery, [
      name, slug, type, description, parent_id, sort_order
    ]);

    const categoryId = (result as unknown as { insertId: number }).insertId;

    // Fetch the created category
    const newCategory = await query(
      'SELECT * FROM categories WHERE id = ?',
      [categoryId]
    );

    return NextResponse.json(newCategory[0], { status: 201 });
  } catch (error) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      id,
      name,
      slug,
      type,
      description,
      parent_id,
      sort_order
    } = body;

    // Validate required fields
    if (!id || !name || !slug || !type) {
      return NextResponse.json(
        { error: 'ID, name, slug, and type are required' },
        { status: 400 }
      );
    }

    // Check if category exists
    const existingCategory = await query(
      'SELECT id FROM categories WHERE id = ?',
      [id]
    );

    if (existingCategory.length === 0) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Check if slug already exists (excluding current category)
    const duplicateSlug = await query(
      'SELECT id FROM categories WHERE slug = ? AND id != ?',
      [slug, id]
    );

    if (duplicateSlug.length > 0) {
      return NextResponse.json(
        { error: 'Category with this slug already exists' },
        { status: 409 }
      );
    }

    // Update category
    const updateQuery = `
      UPDATE categories SET 
        name = ?, slug = ?, type = ?, description = ?, 
        parent_id = ?, sort_order = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await query(updateQuery, [
      name, slug, type, description, parent_id, sort_order, id
    ]);

    // Fetch the updated category
    const updatedCategory = await query(
      'SELECT * FROM categories WHERE id = ?',
      [id]
    );

    return NextResponse.json(updatedCategory[0]);
  } catch (error) {
    console.error('Error updating category:', error);
    return NextResponse.json(
      { error: 'Failed to update category' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      );
    }

    // Check if category exists
    const existingCategory = await query(
      'SELECT id FROM categories WHERE id = ?',
      [id]
    );

    if (existingCategory.length === 0) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // Check if category has children
    const children = await query(
      'SELECT id FROM categories WHERE parent_id = ?',
      [id]
    );

    if (children.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category with subcategories' },
        { status: 400 }
      );
    }

    // Check if category is used by articles or products
    const articlesCount = await query(
      'SELECT COUNT(*) as count FROM articles WHERE category_id = ?',
      [id]
    );

    const productsCount = await query(
      'SELECT COUNT(*) as count FROM products WHERE category_id = ?',
      [id]
    );

    if ((articlesCount[0] as { count: number })?.count > 0 || (productsCount[0] as { count: number })?.count > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category that is in use' },
        { status: 400 }
      );
    }

    // Delete category
    await query('DELETE FROM categories WHERE id = ?', [id]);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting category:', error);
    return NextResponse.json(
      { error: 'Failed to delete category' },
      { status: 500 }
    );
  }
}
