'use client';

import React, { useRef, useState } from 'react';
import {
  Switch,
  message,
  Popconfirm,
  Space,
  Button
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { ProTable, ProForm, ProFormText, ProFormSelect, ProFormTextArea, ProFormDigit, ModalForm } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-table';

interface Product {
  id: number;
  name: string;
  model?: string;
  slug: string;
  category: string;
  description?: string;
  href?: string;
  image?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// interface Pagination {
//   page: number;
//   limit: number;
//   total: number;
//   totalPages: number;
//   hasNext: boolean;
//   hasPrev: boolean;
// }

const ProductsManagement = () => {
  const actionRef = useRef<ActionType>(null);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  const categoryOptions = [
    { label: '真空等离子', value: 'vacuum' },
    { label: '大气等离子', value: 'atmospheric' },
    { label: '医疗设备', value: 'medical' },
    { label: '刻蚀设备', value: 'etching' }
  ];

  // 获取产品列表
  const fetchProducts = async (params: { current?: number; pageSize?: number; name?: string; category?: string; status?: number; is_active?: number }) => {
    try {
      const searchParams = new URLSearchParams({
        page: params.current?.toString() || '1',
        limit: params.pageSize?.toString() || '10',
        ...(params.name && { search: params.name }),
        ...(params.category && { category: params.category }),
        ...(params.is_active !== undefined && { status: params.is_active.toString() })
      });

      const response = await fetch(`/api/admin/products?${searchParams}`);
      const data = await response.json();

      if (data.success) {
        return {
          data: data.data.products,
          success: true,
          total: data.data.pagination.total,
        };
      }
      return {
        data: [],
        success: false,
        total: 0,
      };
    } catch (error) {
      console.error('Error fetching products:', error);
      message.error('获取产品列表失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  // 删除产品
  const handleDelete = async (id: number) => {
    try {
      const response = await fetch(`/api/admin/products?id=${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        message.success('删除成功');
        actionRef.current?.reload();
      } else {
        message.error('删除失败');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      message.error('删除失败');
    }
  };

  // 编辑产品
  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    setModalVisible(true);
  };

  // 创建产品
  const handleCreate = () => {
    setEditingProduct(null);
    setModalVisible(true);
  };

  // 提交表单
  const handleSubmit = async (values: Partial<Product>) => {
    try {
      const url = '/api/admin/products';
      const method = editingProduct ? 'PUT' : 'POST';
      const body = editingProduct ? { ...values, id: editingProduct.id } : values;

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      if (response.ok) {
        message.success(editingProduct ? '更新成功' : '创建成功');
        setModalVisible(false);
        actionRef.current?.reload();
        return true;
      } else {
        message.error(editingProduct ? '更新失败' : '创建失败');
        return false;
      }
    } catch (error) {
      console.error('Error saving product:', error);
      message.error('保存失败');
      return false;
    }
  };

  // ProTable 列配置
  const columns: ProColumns<Product>[] = [
    {
      title: '产品名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (_, record) => (
        <div style={{ maxWidth: 280, minWidth: 180 }}>
          <div style={{
            fontWeight: 600,
            marginBottom: 6,
            fontSize: 14,
            color: '#1a1a1a',
            lineHeight: 1.4,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {record.name}
          </div>
          {record.model && (
            <div style={{
              fontSize: 13,
              color: '#666',
              marginBottom: 4,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              型号: {record.model}
            </div>
          )}
          <div style={{
            fontSize: 11,
            color: '#999',
            fontFamily: 'monospace',
            background: '#f5f5f5',
            padding: '2px 6px',
            borderRadius: 4,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {record.slug}
          </div>
        </div>
      ),
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      valueType: 'select',
      valueEnum: {
        vacuum: { text: '真空等离子', status: 'Processing' },
        atmospheric: { text: '大气等离子', status: 'Success' },
        medical: { text: '医疗设备', status: 'Warning' },
        etching: { text: '刻蚀设备', status: 'Error' }
      },
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      valueType: 'select',
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Error' }
      },
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
      sorter: true,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      valueType: 'dateTime',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      hideInSearch: true,
      render: (_, record) => (
        <Space className="table-action-buttons">
          <a
            onClick={() => handleEdit(record)}
            style={{
              color: '#1890ff',
              cursor: 'pointer',
              fontSize: 14,
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.textDecoration = 'underline';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.textDecoration = 'none';
            }}
          >
            编辑
          </a>
          <Popconfirm
            title="确定要删除这个产品吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            okButtonProps={{
              style: {
                background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
                border: 'none',
                borderRadius: 6,
                minHeight: '36px'
              }
            }}
            cancelButtonProps={{
              style: {
                borderRadius: 6,
                minHeight: '36px'
              }
            }}
          >
            <a
              style={{
                color: '#ff4d4f',
                cursor: 'pointer',
                fontSize: 14,
                textDecoration: 'none'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.textDecoration = 'underline';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.textDecoration = 'none';
              }}
            >
              删除
            </a>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ minHeight: '100%' }}>
      <ProTable<Product>
        headerTitle="产品管理"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          optionRender: (searchConfig, formProps, dom) => [
            ...dom.reverse(),
          ]
        }}
        toolBarRender={() => [
          <Button
            key="create"
            type="primary"
            onClick={handleCreate}
            icon={<PlusOutlined />}
          >
            添加产品
          </Button>,
        ]}
        request={fetchProducts}
        columns={columns}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        scroll={{ x: 1200 }}
        size="middle"
      />

      {/* 产品编辑/创建模态框 */}
      <ModalForm
        title={editingProduct ? '编辑产品' : '添加产品'}
        open={modalVisible}
        onOpenChange={setModalVisible}
        onFinish={handleSubmit}
        initialValues={editingProduct || { is_active: true, sort_order: 0 }}
        width={600}
      >
        <ProForm.Group>
          <ProFormText
            name="name"
            label="产品名称"
            width="md"
            rules={[{ required: true, message: '请输入产品名称' }]}
            placeholder="请输入产品名称"
          />
          <ProFormText
            name="model"
            label="产品型号"
            width="md"
            placeholder="请输入产品型号"
          />
        </ProForm.Group>

        <ProForm.Group>
          <ProFormSelect
            name="category"
            label="产品类别"
            width="md"
            rules={[{ required: true, message: '请选择产品类别' }]}
            options={categoryOptions}
            placeholder="请选择产品类别"
          />
          <ProFormText
            name="image"
            label="产品图片"
            width="md"
            placeholder="请输入图片URL"
          />
        </ProForm.Group>

        <ProFormTextArea
          name="description"
          label="产品描述"
          placeholder="请输入产品描述"
          fieldProps={{ rows: 3 }}
        />

        <ProForm.Group>
          <ProFormDigit
            name="sort_order"
            label="排序"
            width="sm"
            min={0}
            placeholder="请输入排序数字"
          />
          <ProForm.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </ProForm.Item>
        </ProForm.Group>
      </ModalForm>
    </div>
  );
};

export default ProductsManagement;
