'use client';

import React, { useEffect, useState } from 'react';
import { List, Tag, Progress } from 'antd';
import {
  ShoppingOutlined,
  FileTextOutlined,
  EyeOutlined,
  TrophyOutlined,
  ArrowUpOutlined,
  RiseOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ProCard, StatisticCard } from '@ant-design/pro-components';



interface DashboardStats {
  totalProducts: number;
  totalNews: number;
  activeProducts: number;
  publishedNews: number;
}

const AdminDashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalNews: 0,
    activeProducts: 0,
    publishedNews: 0
  });
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      // 获取产品统计
      const productsRes = await fetch('/api/admin/products?limit=1');
      const productsData = await productsRes.json();

      // 获取新闻统计
      const newsRes = await fetch('/api/admin/news?limit=1');
      const newsData = await newsRes.json();

      if (productsData.success && newsData.success) {
        setStats({
          totalProducts: productsData.data.pagination.total,
          totalNews: newsData.data.pagination.total,
          activeProducts: productsData.data.pagination.total, // 简化统计
          publishedNews: newsData.data.pagination.total // 简化统计
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: '添加新产品',
      description: '快速添加等离子清洗设备产品',
      icon: <ShoppingOutlined />,
      action: () => router.push('/admin/products'),
      color: '#1890ff'
    },
    {
      title: '发布新闻',
      description: '发布最新的技术资讯和行业动态',
      icon: <FileTextOutlined />,
      action: () => router.push('/admin/news'),
      color: '#52c41a'
    },
    {
      title: '查看网站',
      description: '预览网站前台页面效果',
      icon: <EyeOutlined />,
      action: () => window.open('/', '_blank'),
      color: '#722ed1'
    }
  ];

  const recentActivities = [
    { title: '系统启动成功', time: '刚刚', type: 'success' },
    { title: '数据库连接正常', time: '1分钟前', type: 'info' },
    { title: '管理员登录', time: '2分钟前', type: 'warning' },
    { title: '缓存清理完成', time: '5分钟前', type: 'success' }
  ];

  return (
    <div style={{ minHeight: '100%' }}>
      {/* 页面标题 */}
      <ProCard
        style={{
          marginBottom: 16,
          background: 'rgba(102, 126, 234, 0.02)',
          border: '1px solid rgba(102, 126, 234, 0.08)',
          borderRadius: 4,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
        }}
        bodyStyle={{ padding: '16px' }}
      >
        <div style={{ textAlign: 'center' }}>
          <div style={{
            fontSize: 24,
            fontWeight: 700,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            marginBottom: 6
          }}>
            仪表盘
          </div>
          <div style={{
            fontSize: 14,
            color: '#666',
            fontWeight: 500
          }}>
            欢迎使用等离子清洗专家管理系统
          </div>
          <div style={{
            fontSize: 12,
            color: '#999',
            marginTop: 6
          }}>
            实时监控系统运行状态，管理产品和新闻内容
          </div>
        </div>
      </ProCard>

      {/* 统计卡片 */}
      <div
        className="statistics-grid"
        style={{ marginBottom: 16 }}
      >
        <ProCard
          style={{
            borderRadius: 4,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            boxShadow: '0 4px 16px rgba(102, 126, 234, 0.2)',
            overflow: 'hidden'
          }}
        >
          <StatisticCard
            statistic={{
              title: '产品总数',
              value: stats.totalProducts,
              icon: <ShoppingOutlined style={{ color: 'white', fontSize: 24 }} />,
              valueStyle: { color: 'white', fontSize: 32, fontWeight: 700 },
              description: (
                <div style={{ color: 'rgba(255,255,255,0.8)', fontSize: 14 }}>
                  <RiseOutlined style={{ marginRight: 4 }} />
                  较上月 +9.3%
                </div>
              ),
            }}
            loading={loading}
            style={{
              background: 'transparent',
              color: 'white'
            }}
            headStyle={{ color: 'rgba(255,255,255,0.9)', fontSize: 16, fontWeight: 600 }}
          />
        </ProCard>
        <ProCard
          style={{
            borderRadius: 4,
            background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
            border: 'none',
            boxShadow: '0 4px 16px rgba(17, 153, 142, 0.2)',
            overflow: 'hidden'
          }}
        >
          <StatisticCard
            statistic={{
              title: '新闻总数',
              value: stats.totalNews,
              icon: <FileTextOutlined style={{ color: 'white', fontSize: 24 }} />,
              valueStyle: { color: 'white', fontSize: 32, fontWeight: 700 },
              description: (
                <div style={{ color: 'rgba(255,255,255,0.8)', fontSize: 14 }}>
                  <RiseOutlined style={{ marginRight: 4 }} />
                  较上月 +12.8%
                </div>
              ),
            }}
            loading={loading}
            style={{
              background: 'transparent',
              color: 'white'
            }}
            headStyle={{ color: 'rgba(255,255,255,0.9)', fontSize: 16, fontWeight: 600 }}
          />
        </ProCard>
        <ProCard
          style={{
            borderRadius: 12,
            background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
            border: 'none',
            boxShadow: '0 8px 32px rgba(252, 182, 159, 0.3)',
            overflow: 'hidden'
          }}
        >
          <StatisticCard
            statistic={{
              title: '启用产品',
              value: stats.activeProducts,
              icon: <TrophyOutlined style={{ color: '#d4380d', fontSize: 24 }} />,
              valueStyle: { color: '#d4380d', fontSize: 32, fontWeight: 700 },
              description: (
                <div style={{ color: '#d4380d', fontSize: 14, opacity: 0.8 }}>
                  启用率 85.7%
                </div>
              ),
            }}
            loading={loading}
            style={{
              background: 'transparent'
            }}
            headStyle={{ color: '#d4380d', fontSize: 16, fontWeight: 600 }}
          />
        </ProCard>
        <ProCard
          style={{
            borderRadius: 12,
            background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
            border: 'none',
            boxShadow: '0 8px 32px rgba(168, 237, 234, 0.3)',
            overflow: 'hidden'
          }}
        >
          <StatisticCard
            statistic={{
              title: '已发布新闻',
              value: stats.publishedNews,
              icon: <ArrowUpOutlined style={{ color: '#722ed1', fontSize: 24 }} />,
              valueStyle: { color: '#722ed1', fontSize: 32, fontWeight: 700 },
              description: (
                <div style={{ color: '#722ed1', fontSize: 14, opacity: 0.8 }}>
                  发布率 92.3%
                </div>
              ),
            }}
            loading={loading}
            style={{
              background: 'transparent'
            }}
            headStyle={{ color: '#722ed1', fontSize: 16, fontWeight: 600 }}
          />
        </ProCard>
      </div>

      <ProCard gutter={[24, 24]}>
        {/* 快速操作 */}
        <ProCard
          colSpan={12}
          title={
            <span style={{
              fontSize: 18,
              fontWeight: 600,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              快速操作
            </span>
          }
          style={{
            borderRadius: 12,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            border: '1px solid rgba(102, 126, 234, 0.1)'
          }}
          headerBordered
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
            {quickActions.map((action, index) => (
              <div
                key={index}
                onClick={action.action}
                style={{
                  cursor: 'pointer',
                  padding: 20,
                  borderRadius: 12,
                  background: `linear-gradient(135deg, ${action.color}10 0%, ${action.color}05 100%)`,
                  border: `1px solid ${action.color}20`,
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = `0 8px 25px ${action.color}30`;
                  e.currentTarget.style.borderColor = `${action.color}40`;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.borderColor = `${action.color}20`;
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{
                    fontSize: 32,
                    color: action.color,
                    marginRight: 20,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 56,
                    height: 56,
                    borderRadius: 12,
                    background: `${action.color}15`,
                    border: `2px solid ${action.color}30`
                  }}>
                    {action.icon}
                  </div>
                  <div>
                    <div style={{
                      fontWeight: 700,
                      marginBottom: 6,
                      fontSize: 18,
                      color: '#333'
                    }}>
                      {action.title}
                    </div>
                    <div style={{
                      color: '#666',
                      fontSize: 14,
                      lineHeight: 1.4
                    }}>
                      {action.description}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ProCard>

        {/* 最近活动 */}
        <ProCard
          colSpan={12}
          title={
            <span style={{
              fontSize: 18,
              fontWeight: 600,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              最近活动
            </span>
          }
          style={{
            borderRadius: 12,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            border: '1px solid rgba(102, 126, 234, 0.1)'
          }}
          headerBordered
        >
          <List
            dataSource={recentActivities}
            renderItem={(item, index) => (
              <List.Item style={{
                padding: '16px 0',
                borderBottom: index === recentActivities.length - 1 ? 'none' : '1px solid #f5f5f5',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(102, 126, 234, 0.05)';
                e.currentTarget.style.borderRadius = '8px';
                e.currentTarget.style.padding = '16px 12px';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'transparent';
                e.currentTarget.style.borderRadius = '0';
                e.currentTarget.style.padding = '16px 0';
              }}
              >
                <List.Item.Meta
                  avatar={
                    <div style={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      background: item.type === 'success' ? 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)' :
                                  item.type === 'warning' ? 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' :
                                  'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: 16,
                      boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                    }}>
                      <ClockCircleOutlined />
                    </div>
                  }
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{
                        fontSize: 15,
                        fontWeight: 600,
                        color: '#333'
                      }}>
                        {item.title}
                      </span>
                      <Tag
                        style={{
                          background: item.type === 'success' ? 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)' :
                                      item.type === 'warning' ? 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' :
                                      'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          color: item.type === 'warning' ? '#d4380d' : 'white',
                          border: 'none',
                          borderRadius: 6,
                          fontSize: 12,
                          fontWeight: 500,
                          padding: '2px 8px'
                        }}
                      >
                        {item.time}
                      </Tag>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </ProCard>
      </ProCard>

      {/* 系统状态 */}
      <ProCard
        title="系统状态"
        headerBordered
        style={{ marginTop: 16 }}
        gutter={[16, 16]}
      >
        <ProCard colSpan={8}>
          <StatisticCard
            statistic={{
              title: '服务器状态',
              value: '正常运行',
              icon: <ArrowUpOutlined style={{ color: '#52c41a' }} />,
              status: 'success',
              description: (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Progress
                    percent={98}
                    size="small"
                    status="active"
                    showInfo={false}
                    style={{ flex: 1 }}
                  />
                  <span style={{ fontSize: 12, color: '#52c41a' }}>98%</span>
                </div>
              ),
            }}
          />
        </ProCard>
        <ProCard colSpan={8}>
          <StatisticCard
            statistic={{
              title: '数据库连接',
              value: '稳定',
              icon: <ArrowUpOutlined style={{ color: '#52c41a' }} />,
              status: 'success',
              description: (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Progress
                    percent={95}
                    size="small"
                    status="active"
                    showInfo={false}
                    style={{ flex: 1 }}
                  />
                  <span style={{ fontSize: 12, color: '#52c41a' }}>95%</span>
                </div>
              ),
            }}
          />
        </ProCard>
        <ProCard colSpan={8}>
          <StatisticCard
            statistic={{
              title: '系统版本',
              value: 'v2.0.0',
              icon: <TrophyOutlined style={{ color: '#1890ff' }} />,
              status: 'processing',
              description: (
                <div style={{ fontSize: 12, color: '#666' }}>
                  Pro Components版本
                </div>
              ),
            }}
          />
        </ProCard>
      </ProCard>
    </div>
  );
};

export default AdminDashboard;
