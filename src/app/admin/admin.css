/* 管理后台全局样式 */

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
  }
}

/* 全局样式覆盖 */
.ant-pro-layout {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
}

.ant-pro-layout-header {
  backdrop-filter: blur(10px) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1) !important;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08) !important;
}

.ant-pro-layout-sider {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-right: 1px solid rgba(102, 126, 234, 0.1) !important;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.05) !important;
}

.ant-menu-item {
  border-radius: 6px !important;
  margin: 4px 8px !important;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
  padding: 5px 12px !important;
}

.ant-menu-item:hover:not(.ant-menu-item-selected) {
  background: rgba(24, 144, 255, 0.06) !important;
  color: #1890ff !important;
}

.ant-menu-item-selected {
  background: #e6f7ff !important;
  color: #1890ff !important;
}

.ant-menu-item-selected .ant-menu-item-icon {
  color: #1890ff !important;
}

.ant-menu-item-selected:hover {
  background: #e6f7ff !important;
  color: #1890ff !important;
}

/* ProTable 样式优化 */
.ant-pro-table-search {
  background: #fafafa !important;
  border-radius: 6px !important;
  border: 1px solid #f0f0f0 !important;
  padding: 16px !important;
}

.ant-table-thead > tr > th {
  background: #fafafa !important;
  border-bottom: 1px solid #f0f0f0 !important;
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 0.85) !important;
  font-size: 14px !important;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5 !important;
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid rgba(102, 126, 234, 0.05) !important;
  transition: all 0.3s ease !important;
}

/* 按钮样式优化 - 遵循Ant Design简约原则 */
.ant-btn {
  border-radius: 6px !important;
  padding: 4px 15px !important;
  height: 32px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 1.5715 !important;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
}

.ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045) !important;
}

.ant-btn-primary:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045) !important;
}

.ant-btn-primary:focus {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 危险按钮样式 */
.ant-btn-dangerous {
  background: #ff4d4f !important;
  border-color: #ff4d4f !important;
}

.ant-btn-dangerous:hover {
  background: #ff7875 !important;
  border-color: #ff7875 !important;
}

/* 表单样式 - 使用Ant Design默认样式 */

/* 卡片样式优化 */
.ant-pro-card {
  border-radius: 6px !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02) !important;
  border: 1px solid #f0f0f0 !important;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !important;
}

.ant-pro-card:hover {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 2px 8px -1px rgba(0, 0, 0, 0.05), 0 4px 4px 0 rgba(0, 0, 0, 0.02) !important;
}

.ant-pro-card .ant-pro-card-body {
  padding: 16px !important;
}

/* 统计卡片动画 */
.ant-statistic-card {
  animation: fadeInUp 0.6s ease-out !important;
}

/* 模态框样式优化 */
.ant-modal {
  border-radius: 6px !important;
  overflow: hidden !important;
}

.ant-modal-header {
  background: #fff !important;
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 16px 24px !important;
}

.ant-modal-body {
  padding: 24px !important;
}

.ant-modal-title {
  font-size: 16px !important;
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 0.85) !important;
}

/* 分页样式 - 使用Ant Design默认样式 */

/* 标签样式优化 */
.ant-tag {
  border-radius: 4px !important;
  font-weight: 500 !important;
  padding: 4px 8px !important;
  border: none !important;
}

/* 开关样式优化 */
.ant-switch {
  background: #f0f0f0 !important;
  border-radius: 4px !important;
}

.ant-switch-checked {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* 消息提示样式优化 */
.ant-message {
  top: 80px !important;
}

.ant-message-notice {
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* 表格操作按钮优化 */
.ant-table-tbody .ant-btn {
  height: 28px !important;
  padding: 0 8px !important;
  font-size: 12px !important;
  margin-right: 8px !important;
}

.ant-table-tbody .ant-btn:last-child {
  margin-right: 0 !important;
}

/* 表格操作列样式 */
.ant-table-tbody > tr > td {
  padding: 12px 16px !important;
  vertical-align: middle !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .ant-pro-layout-sider {
    width: 0 !important;
  }

  .ant-pro-card {
    margin: 6px !important;
  }

  .ant-table {
    font-size: 14px !important;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px !important;
    min-height: 44px !important;
  }

  /* 移动端表格操作按钮 */
  .ant-table-tbody .ant-btn {
    height: 24px !important;
    padding: 0 6px !important;
    font-size: 11px !important;
    margin-right: 4px !important;
    margin-bottom: 2px !important;
  }

  /* 移动端模态框 */
  .ant-modal {
    width: 95vw !important;
    margin: 8px auto !important;
    max-width: none !important;
    border-radius: 6px !important;
  }

  .ant-modal-body {
    padding: 16px !important;
  }

  .ant-modal-header {
    padding: 16px 16px 8px !important;
  }

  /* 移动端统计卡片 */
  .ant-statistic-card {
    margin-bottom: 12px !important;
  }

  .ant-statistic-title {
    font-size: 12px !important;
  }

  .ant-statistic-content-value {
    font-size: 20px !important;
  }

  /* 移动端搜索表单 */
  .ant-pro-table-search {
    padding: 12px !important;
    border-radius: 6px !important;
  }

  .ant-form-item {
    margin-bottom: 12px !important;
  }

  /* 移动端输入框 - 使用Ant Design默认样式 */
  .ant-input {
    /* 移除所有自定义样式，使用Ant Design默认样式 */
  }

  .ant-select-selector {
    /* 移除所有自定义样式，使用Ant Design默认样式 */
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(102, 126, 234, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%);
}

/* 加载动画 */
.ant-spin-dot {
  animation: pulse 1.5s infinite !important;
}

/* 面包屑样式 */
.ant-breadcrumb {
  font-weight: 500 !important;
}

.ant-breadcrumb a {
  color: #667eea !important;
  transition: all 0.3s ease !important;
}

.ant-breadcrumb a:hover {
  color: #764ba2 !important;
}

/* 登录页面优化 */
.login-container {
  position: relative;
  overflow: hidden;
}

.login-card {
  transition: all 0.3s ease;
}

@media (max-width: 480px) {
  .login-card {
    width: calc(100vw - 16px) !important;
    margin: 8px !important;
    max-width: 380px !important;
    border-radius: 4px !important;
  }

  .login-form-header {
    padding: 12px !important;
  }

  .login-form-body {
    padding: 12px !important;
  }
}

/* 统计卡片网格优化 */
.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

@media (max-width: 768px) {
  .statistics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* 触摸友好的按钮尺寸 */
@media (max-width: 768px) {
  .touch-friendly-button {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 8px 16px !important;
  }

  .ant-btn {
    min-height: 40px !important;
  }

  .ant-btn-sm {
    min-height: 36px !important;
  }
}

/* 表单验证错误优化 */
.ant-form-item-explain-error {
  margin-top: 4px !important;
  padding: 4px 8px !important;
  background: rgba(255, 77, 79, 0.1) !important;
  border-radius: 4px !important;
  border-left: 3px solid #ff4d4f !important;
  font-size: 12px !important;
}

/* 加载骨架屏样式 */
.statistic-skeleton {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
}

.statistic-skeleton .ant-skeleton-avatar {
  margin-bottom: 16px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #999;
}

.empty-state-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-state-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-state-description {
  font-size: 14px;
  color: #999;
}

/* 性能优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .ant-btn-primary {
    background: #000 !important;
    border-color: #000 !important;
  }

  .ant-table-thead > tr > th {
    background: #f0f0f0 !important;
    border-bottom: 2px solid #000 !important;
  }
}

/* 触摸友好性优化 */
.touchable {
  min-height: 44px !important;
  min-width: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  touch-action: manipulation !important;
}

/* 按钮最小尺寸 */
.ant-btn {
  min-height: 36px !important;
  padding: 6px 16px !important;
  font-weight: 500 !important;
}

.ant-btn-lg {
  min-height: 44px !important;
  padding: 8px 20px !important;
  font-size: 16px !important;
}

.ant-btn-sm {
  min-height: 32px !important;
  padding: 4px 12px !important;
  font-size: 14px !important;
}

/* 输入框聚焦 - 使用Ant Design默认样式 */

/* 文本对比度优化 */
.primary-text {
  color: #1a1a1a !important;
  font-weight: 600 !important;
}

.secondary-text {
  color: #4a4a4a !important;
  font-weight: 500 !important;
}

.muted-text {
  color: #6a6a6a !important;
  font-weight: 400 !important;
}

/* 错误状态 - 使用Ant Design默认样式 */

.ant-form-item-explain-error {
  color: #ff4d4f !important;
  font-size: 14px !important;
  margin-top: 4px !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
}

/* 加载状态优化 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
  background-size: 200% 100% !important;
  animation: loading-shimmer 1.5s infinite !important;
  border-radius: 6px !important;
}

@keyframes loading-shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 字体层次系统 */
.text-h1 {
  font-size: 32px !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  color: #1a1a1a !important;
}

.text-h2 {
  font-size: 24px !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  color: #1a1a1a !important;
}

.text-h3 {
  font-size: 20px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  color: #2a2a2a !important;
}

.text-body {
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
  color: #3a3a3a !important;
}

.text-small {
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
  color: #4a4a4a !important;
}

.text-caption {
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 1.4 !important;
  color: #6a6a6a !important;
}
