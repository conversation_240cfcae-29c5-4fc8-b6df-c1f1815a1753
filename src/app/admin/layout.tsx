'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Form, Input, Alert, Spin, Dropdown } from 'antd';
import { ProLayout, ProCard } from '@ant-design/pro-components';
import {
  DashboardOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  LogoutOutlined,
  HomeOutlined,
  UserOutlined,
  LockOutlined
} from '@ant-design/icons';
import type { MenuDataItem } from '@ant-design/pro-layout';
import './admin.css';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loginForm] = Form.useForm();
  const [loginError, setLoginError] = useState('');
  const router = useRouter();
  const pathname = usePathname();

  // 简单的密码验证 - 在实际项目中应该使用更安全的认证方式
  const ADMIN_PASSWORD = 'admin123';

  useEffect(() => {
    // 检查是否已经登录
    const isLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
    setIsAuthenticated(isLoggedIn);
    setIsLoading(false);
  }, []);

  const handleLogin = async (values: { password: string }) => {
    if (values.password === ADMIN_PASSWORD) {
      localStorage.setItem('admin_logged_in', 'true');
      setIsAuthenticated(true);
      setLoginError('');
    } else {
      setLoginError('密码错误');
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('admin_logged_in');
    setIsAuthenticated(false);
    router.push('/');
  };

  // Pro Layout 菜单配置
  const menuData: MenuDataItem[] = [
    {
      path: '/admin',
      name: '仪表盘',
      icon: <DashboardOutlined />,
    },
    {
      path: '/admin/products',
      name: '产品管理',
      icon: <ShoppingOutlined />,
    },
    {
      path: '/admin/news',
      name: '新闻管理',
      icon: <FileTextOutlined />,
    },
  ];

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '返回网站',
      onClick: () => window.open('/', '_blank'),
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  if (isLoading) {
    return (
      <div style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div
        className="login-container"
        style={{
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          position: 'relative',
          overflow: 'hidden',
          padding: '16px'
        }}
      >
        {/* 背景装饰 */}
        <div style={{
          position: 'absolute',
          top: '-50%',
          left: '-50%',
          width: '200%',
          height: '200%',
          background: 'radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)',
          backgroundSize: '50px 50px',
          animation: 'float 20s ease-in-out infinite',
          opacity: 0.3
        }} />

        <ProCard
          className="login-card"
          style={{
            width: '100%',
            maxWidth: 400,
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)',
            boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
            borderRadius: 4,
            border: '1px solid rgba(255, 255, 255, 0.2)',
            overflow: 'hidden'
          }}
          bodyStyle={{ padding: 0 }}
        >
          {/* 头部装饰 */}
          <div
            className="login-form-header"
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              padding: '16px',
              textAlign: 'center',
              color: 'white'
            }}
          >
            <div style={{
              width: 48,
              height: 48,
              background: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 4,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 12px',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.3)'
            }}>
              <LockOutlined style={{ fontSize: 24 }} />
            </div>
            <div style={{ fontSize: 20, fontWeight: 700, marginBottom: 4 }}>
              管理后台登录
            </div>
            <div style={{ fontSize: 14, opacity: 0.9 }}>
              等离子清洗专家 - 管理系统
            </div>
          </div>

          <div className="login-form-body" style={{ padding: '16px' }}>
            <Form
              form={loginForm}
              onFinish={handleLogin}
              layout="vertical"
              size="large"
            >
              <Form.Item
                name="password"
                label={<span style={{ fontWeight: 600, color: '#333' }}>管理密码</span>}
                rules={[{ required: true, message: '请输入管理密码' }]}
              >
                <Input.Password
                  placeholder="请输入管理密码"
                  prefix={<LockOutlined style={{ color: '#667eea' }} />}
                  style={{
                    borderRadius: 4,
                    border: '1px solid #e0e0e0',
                    transition: 'all 0.3s ease',
                    padding: '8px 12px'
                  }}
                />
              </Form.Item>

              {loginError && (
                <Alert
                  message={loginError}
                  type="error"
                  showIcon
                  style={{
                    marginBottom: 16,
                    borderRadius: 8,
                    border: 'none'
                  }}
                />
              )}

              <Form.Item style={{ marginBottom: 16 }}>
                <button
                  type="submit"
                  style={{
                    width: '100%',
                    height: 40,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    border: 'none',
                    borderRadius: 4,
                    color: 'white',
                    fontSize: 14,
                    fontWeight: 600,
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 2px 8px rgba(102, 126, 234, 0.2)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-1px)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(102, 126, 234, 0.2)';
                  }}
                >
                  登录系统
                </button>
              </Form.Item>

              <div style={{ textAlign: 'center', marginBottom: 16 }}>
                <button
                  type="button"
                  onClick={() => window.open('/', '_blank')}
                  style={{
                    background: 'none',
                    border: '1px solid rgba(102, 126, 234, 0.3)',
                    borderRadius: 4,
                    padding: '6px 12px',
                    color: '#667eea',
                    cursor: 'pointer',
                    fontSize: 12,
                    fontWeight: 500,
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(102, 126, 234, 0.05)';
                    e.currentTarget.style.borderColor = '#667eea';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'none';
                    e.currentTarget.style.borderColor = 'rgba(102, 126, 234, 0.3)';
                  }}
                >
                  <HomeOutlined style={{ marginRight: 6 }} />
                  返回网站首页
                </button>
              </div>
            </Form>

            <Alert
              message="演示说明"
              description={
                <div>
                  <div>默认密码：<strong style={{ color: '#667eea' }}>admin123</strong></div>
                  <div style={{ fontSize: 12, marginTop: 4, color: '#666' }}>
                    注意：这是演示版本，实际使用时请修改为更安全的认证方式
                  </div>
                </div>
              }
              type="info"
              showIcon
              style={{
                borderRadius: 8,
                border: 'none',
                background: 'rgba(102, 126, 234, 0.05)'
              }}
            />
          </div>
        </ProCard>

        <style jsx>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <ProLayout
      title="等离子清洗专家"
      logo={
        <div style={{
          width: 32,
          height: 32,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: 8,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: 16,
          fontWeight: 600,
          boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)'
        }}>
          等
        </div>
      }
      layout="mix"
      splitMenus={false}
      navTheme="light"
      fixedHeader
      fixSiderbar
      colorWeak={false}
      siderWidth={256}
      menu={{
        type: 'group',
        request: async () => menuData,
      }}
      location={{
        pathname,
      }}
      avatarProps={{
        src: undefined,
        title: '管理员',
        size: 'default',
        style: {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white'
        },
        icon: <UserOutlined />,
        render: (props, dom) => {
          return (
            <Dropdown
              menu={{
                items: userMenuItems,
              }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                padding: '8px 12px',
                borderRadius: 8,
                transition: 'all 0.3s ease',
                background: 'rgba(102, 126, 234, 0.1)',
                border: '1px solid rgba(102, 126, 234, 0.2)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(102, 126, 234, 0.15)';
                e.currentTarget.style.transform = 'translateY(-1px)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(102, 126, 234, 0.1)';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
              >
                {dom}
                <span style={{
                  marginLeft: 8,
                  fontWeight: 500,
                  color: '#667eea'
                }}>管理员</span>
              </div>
            </Dropdown>
          );
        },
      }}
      actionsRender={() => [
        <div key="actions" style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
          <button
            onClick={() => window.open('/', '_blank')}
            style={{
              background: 'none',
              border: '1px solid rgba(102, 126, 234, 0.3)',
              borderRadius: 6,
              padding: '6px 12px',
              color: '#667eea',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: 6,
              fontSize: 14,
              fontWeight: 500,
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#667eea';
              e.currentTarget.style.color = 'white';
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'none';
              e.currentTarget.style.color = '#667eea';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            <HomeOutlined />
            返回网站
          </button>
        </div>
      ]}
      menuItemRender={(item, dom) => (
        <div
          onClick={() => {
            router.push(item.path || '/admin');
          }}
          style={{
            borderRadius: 6,
            margin: '2px 8px',
            transition: 'all 0.3s ease'
          }}
        >
          {dom}
        </div>
      )}
      breadcrumbRender={(routers = []) => [
        {
          path: '/admin',
          breadcrumbName: '首页',
        },
        ...routers,
      ]}
      style={{
        minHeight: '100vh',
      }}
      contentStyle={{
        padding: 0,
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        minHeight: '100vh'
      }}
    >
      <div style={{
        padding: 24,
        minHeight: 'calc(100vh - 112px)',
      }}>
        {children}
      </div>
    </ProLayout>
  );
};

export default AdminLayout;
