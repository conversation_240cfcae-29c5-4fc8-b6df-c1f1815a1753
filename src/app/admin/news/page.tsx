'use client';

import React, { useRef, useState } from 'react';
import {
  Switch,
  message,
  Popconfirm,
  Space,
  Button
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { ProTable, ProForm, ProFormText, ProFormSelect, ProFormTextArea, ProFormDigit, ProFormDatePicker, ModalForm } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import dayjs from 'dayjs';

// 生成URL slug的工具函数
const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/-+/g, '-') // 多个连字符合并为一个
    .trim()
    .substring(0, 50); // 限制长度
};

interface News {
  id: number;
  title: string;
  excerpt?: string;
  content?: string;
  category: string;
  href?: string;
  publish_date: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// interface Pagination {
//   page: number;
//   limit: number;
//   total: number;
//   totalPages: number;
//   hasNext: boolean;
//   hasPrev: boolean;
// }

const NewsManagement = () => {
  const actionRef = useRef<ActionType>(null);
  const [editingNews, setEditingNews] = useState<News | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  const categoryOptions = [
    { label: '等离子清洗机百科', value: 'encyclopedia' },
    { label: '等离子清洗机应用', value: 'applications' },
    { label: '技术资讯', value: 'technology' },
    { label: '行业动态', value: 'industry' }
  ];

  // 获取新闻列表
  const fetchNews = async (params: { current?: number; pageSize?: number; title?: string; status?: number; category?: string; is_active?: number }) => {
    try {
      const searchParams = new URLSearchParams({
        page: params.current?.toString() || '1',
        limit: params.pageSize?.toString() || '10',
        ...(params.title && { search: params.title }),
        ...(params.category && { category: params.category }),
        ...(params.is_active !== undefined && { status: params.is_active.toString() })
      });

      const response = await fetch(`/api/admin/news?${searchParams}`);
      const data = await response.json();

      if (data.success) {
        return {
          data: data.data.news,
          success: true,
          total: data.data.pagination.total,
        };
      }
      return {
        data: [],
        success: false,
        total: 0,
      };
    } catch (error) {
      console.error('Error fetching news:', error);
      message.error('获取新闻列表失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  // 删除新闻
  const handleDelete = async (id: number) => {
    try {
      const response = await fetch(`/api/admin/news?id=${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        message.success('删除成功');
        actionRef.current?.reload();
      } else {
        message.error('删除失败');
      }
    } catch (error) {
      console.error('Error deleting news:', error);
      message.error('删除失败');
    }
  };

  // 编辑新闻
  const handleEdit = (newsItem: News) => {
    setEditingNews(newsItem);
    setModalVisible(true);
  };

  // 创建新闻
  const handleCreate = () => {
    setEditingNews(null);
    setModalVisible(true);
  };

  // 提交表单
  const handleSubmit = async (values: Partial<News>) => {
    try {
      // 如果没有手动设置href，则自动生成
      if (!values.href && values.title) {
        const slug = generateSlug(values.title);
        values.href = `/news/${slug}`;
      }

      // 如果摘要为空，自动生成摘要（取内容前100个字符）
      if (!values.excerpt && values.content) {
        // 移除HTML标签并截取前100个字符
        const plainText = values.content.replace(/<[^>]*>/g, '').trim();
        values.excerpt = plainText.length > 100
          ? plainText.substring(0, 100) + '...'
          : plainText;
      }

      // 转换日期格式
      if (values.publish_date) {
        values.publish_date = dayjs(values.publish_date).format('YYYY-MM-DD');
      }

      const url = '/api/admin/news';
      const method = editingNews ? 'PUT' : 'POST';
      const body = editingNews ? { ...values, id: editingNews.id } : values;

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      if (response.ok) {
        message.success(editingNews ? '更新成功' : '创建成功');
        setModalVisible(false);
        actionRef.current?.reload();
        return true;
      } else {
        message.error(editingNews ? '更新失败' : '创建失败');
        return false;
      }
    } catch (error) {
      console.error('Error saving news:', error);
      message.error('保存失败');
      return false;
    }
  };

  // ProTable 列配置
  const columns: ProColumns<News>[] = [
    {
      title: '新闻标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (_, record) => (
        <div style={{ maxWidth: 320, minWidth: 200 }}>
          <div style={{
            fontWeight: 600,
            marginBottom: 6,
            fontSize: 14,
            color: '#1a1a1a',
            lineHeight: 1.4,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {record.title}
          </div>
          {record.excerpt && (
            <div style={{
              fontSize: 13,
              color: '#666',
              lineHeight: 1.4,
              marginBottom: 4,
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}>
              {record.excerpt.length > 80 ? `${record.excerpt.substring(0, 80)}...` : record.excerpt}
            </div>
          )}
          {record.href && (
            <div style={{
              fontSize: 11,
              color: '#999',
              fontFamily: 'monospace',
              background: '#f5f5f5',
              padding: '2px 6px',
              borderRadius: 4,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {record.href}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 140,
      valueType: 'select',
      valueEnum: {
        encyclopedia: { text: '等离子清洗机百科', status: 'Processing' },
        applications: { text: '等离子清洗机应用', status: 'Success' },
        technology: { text: '技术资讯', status: 'Warning' },
        industry: { text: '行业动态', status: 'Error' }
      },
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      valueType: 'select',
      valueEnum: {
        true: { text: '已发布', status: 'Success' },
        false: { text: '草稿', status: 'Default' }
      },
    },
    {
      title: '发布日期',
      dataIndex: 'publish_date',
      key: 'publish_date',
      width: 120,
      valueType: 'date',
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right',
      hideInSearch: true,
      render: (_, record) => (
        <Space className="table-action-buttons">
          <a
            onClick={() => handleEdit(record)}
            style={{
              color: '#1890ff',
              cursor: 'pointer',
              fontSize: 14,
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.textDecoration = 'underline';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.textDecoration = 'none';
            }}
          >
            编辑
          </a>
          <Popconfirm
            title="确定要删除这条新闻吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            okButtonProps={{
              style: {
                background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
                border: 'none',
                borderRadius: 6,
                minHeight: '36px'
              }
            }}
            cancelButtonProps={{
              style: {
                borderRadius: 6,
                minHeight: '36px'
              }
            }}
          >
            <a
              style={{
                color: '#ff4d4f',
                cursor: 'pointer',
                fontSize: 14,
                textDecoration: 'none'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.textDecoration = 'underline';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.textDecoration = 'none';
              }}
            >
              删除
            </a>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ minHeight: '100%' }}>
      <ProTable<News>
        headerTitle="新闻管理"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          optionRender: (searchConfig, formProps, dom) => [
            ...dom.reverse(),
          ]
        }}
        toolBarRender={() => [
          <Button
            key="create"
            type="primary"
            onClick={handleCreate}
            icon={<PlusOutlined />}
          >
            发布新闻
          </Button>,
        ]}
        request={fetchNews}
        columns={columns}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
        }}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
        scroll={{ x: 1400 }}
        size="middle"
      />

      {/* 新闻编辑/创建模态框 */}
      <ModalForm
        title={editingNews ? '编辑新闻' : '发布新闻'}
        open={modalVisible}
        onOpenChange={setModalVisible}
        onFinish={handleSubmit}
        initialValues={editingNews ? {
          ...editingNews,
          publish_date: editingNews.publish_date ? dayjs(editingNews.publish_date) : dayjs()
        } : {
          is_active: true,
          sort_order: 0,
          publish_date: dayjs()
        }}
        width={800}
      >
        <ProFormText
          name="title"
          label="新闻标题"
          rules={[{ required: true, message: '请输入新闻标题' }]}
          placeholder="请输入新闻标题"
        />

        <ProFormTextArea
          name="excerpt"
          label="新闻摘要"
          placeholder="请输入新闻摘要（可选，留空将自动生成）"
          fieldProps={{ rows: 2 }}
        />

        <ProFormTextArea
          name="content"
          label="新闻内容"
          placeholder="请输入新闻内容"
          fieldProps={{ rows: 6 }}
        />

        <ProForm.Group>
          <ProFormSelect
            name="category"
            label="新闻类别"
            width="md"
            rules={[{ required: true, message: '请选择新闻类别' }]}
            options={categoryOptions}
            placeholder="请选择新闻类别"
          />
          <ProFormDatePicker
            name="publish_date"
            label="发布日期"
            width="md"
            rules={[{ required: true, message: '请选择发布日期' }]}
          />
        </ProForm.Group>

        <ProForm.Group>
          <ProFormDigit
            name="sort_order"
            label="排序"
            width="sm"
            min={0}
            placeholder="请输入排序数字"
          />
          <ProForm.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="已发布" unCheckedChildren="草稿" />
          </ProForm.Item>
        </ProForm.Group>
      </ModalForm>
    </div>
  );
};

export default NewsManagement;
