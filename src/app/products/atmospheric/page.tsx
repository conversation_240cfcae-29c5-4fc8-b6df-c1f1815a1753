'use client';

import Layout from '@/components/Layout';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function AtmosphericProducts() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const products = [
    {
      name: "AP-PM1000 大气等离子清洗设备",
      model: "AP-PM1000",
      features: ["常压操作", "连续处理", "节能环保", "操作简便"],
      applications: ["纺织印染", "塑料改性", "玻璃清洗", "金属表面处理"],
      specs: {
        "处理宽度": "1000mm",
        "处理速度": "1-10m/min",
        "功率": "5000W",
        "气体": "空气、氮气、氩气"
      }
    },
    {
      name: "AP-800-AJR 薄膜等离子清洗机",
      model: "AP-800-AJR",
      features: ["薄膜专用", "均匀处理", "高效率", "低损伤"],
      applications: ["薄膜材料", "柔性电子", "包装材料", "光学薄膜"],
      specs: {
        "处理宽度": "800mm",
        "处理速度": "2-15m/min",
        "功率": "3000W",
        "气体": "空气、氮气"
      }
    },
    {
      name: "AP-500 手持式等离子清洗机",
      model: "AP-500",
      features: ["便携式设计", "灵活操作", "即开即用", "维护简单"],
      applications: ["局部处理", "维修清洗", "现场作业", "小面积处理"],
      specs: {
        "处理宽度": "20mm",
        "功率": "500W",
        "重量": "1.5kg",
        "气体": "压缩空气"
      }
    }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-600 via-teal-600 to-blue-700 text-white py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className={`mb-8 transition-all duration-1000 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              <span className="inline-block bg-white/20 text-white px-6 py-3 rounded-full text-lg font-medium backdrop-blur-sm border border-white/30">
                产品系列
              </span>
            </div>
            <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-8 transition-all duration-1200 delay-200 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              大气等离子清洗机
            </h1>
            <p className={`text-xl md:text-2xl text-green-100 leading-relaxed max-w-3xl mx-auto transition-all duration-1400 delay-400 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              常压下高效表面处理的专业设备
            </p>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-1 gap-12">
              {products.map((product, index) => (
                <div 
                  key={index}
                  className={`bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Product Image */}
                    <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-2xl p-8 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-6xl mb-4">⚡</div>
                        <h3 className="text-2xl font-bold text-gray-800 mb-2">{product.name}</h3>
                        <div className="text-green-600 font-medium">{product.model}</div>
                      </div>
                    </div>

                    {/* Product Details */}
                    <div>
                      <h3 className="text-2xl font-bold text-gray-800 mb-6">{product.name}</h3>
                      
                      {/* Features */}
                      <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-700 mb-3">产品特点</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {product.features.map((feature, idx) => (
                            <div key={idx} className="flex items-center text-gray-600">
                              <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                              {feature}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Applications */}
                      <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-700 mb-3">应用领域</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {product.applications.map((app, idx) => (
                            <div key={idx} className="flex items-center text-gray-600">
                              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                              {app}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Specifications */}
                      <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-700 mb-3">技术参数</h4>
                        <div className="bg-gray-50 rounded-lg p-4">
                          {Object.entries(product.specs).map(([key, value], idx) => (
                            <div key={idx} className="flex justify-between py-1 border-b border-gray-200 last:border-b-0">
                              <span className="text-gray-600">{key}:</span>
                              <span className="font-medium text-gray-800">{value}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex gap-4">
                        <Link
                          href="/contact"
                          className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
                        >
                          咨询报价
                        </Link>
                        <Link
                          href="/demo"
                          className="border border-green-600 text-green-600 px-6 py-3 rounded-lg hover:bg-green-50 transition-colors"
                        >
                          3D演示
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
