'use client';

import Layout from '@/components/Layout';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function SmallVacuumProducts() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const products = [
    {
      name: "PM-20LN 小型等离子清洗机",
      model: "PM-20LN",
      features: ["真空度高", "操作简便", "适合实验室", "成本效益高"],
      applications: ["电子元件清洗", "材料表面改性", "实验室研究", "小批量生产"],
      specs: {
        "腔体尺寸": "200×200×150mm",
        "真空度": "≤10Pa",
        "功率": "100W",
        "气体": "氩气、氧气、氮气"
      }
    },
    {
      name: "PM-3LN 超小型等离子清洗机",
      model: "PM-3LN",
      features: ["体积小巧", "桌面式设计", "自动化程度高", "维护简单"],
      applications: ["精密器件清洗", "表面活化", "去除有机污染", "提高粘接性"],
      specs: {
        "腔体尺寸": "150×150×100mm",
        "真空度": "≤5Pa",
        "功率": "50W",
        "气体": "氩气、氧气"
      }
    },
    {
      name: "PM-210LN 医疗导管等离子清洗机",
      model: "PM-210LN",
      features: ["专业医疗级", "无污染清洗", "符合医疗标准", "高效杀菌"],
      applications: ["医疗器械清洗", "导管表面处理", "生物材料改性", "医疗植入物"],
      specs: {
        "腔体尺寸": "300×200×150mm",
        "真空度": "≤1Pa",
        "功率": "200W",
        "气体": "氩气、氧气、氮气、氢气"
      }
    }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className={`mb-8 transition-all duration-1000 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              <span className="inline-block bg-white/20 text-white px-6 py-3 rounded-full text-lg font-medium backdrop-blur-sm border border-white/30">
                产品系列
              </span>
            </div>
            <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-8 transition-all duration-1200 delay-200 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              小型真空等离子清洗机
            </h1>
            <p className={`text-xl md:text-2xl text-blue-100 leading-relaxed max-w-3xl mx-auto transition-all duration-1400 delay-400 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              适用于实验室和小批量生产的专业等离子清洗设备
            </p>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-1 gap-12">
              {products.map((product, index) => (
                <div 
                  key={index}
                  className={`bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Product Image */}
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-6xl mb-4">🔬</div>
                        <h3 className="text-2xl font-bold text-gray-800 mb-2">{product.name}</h3>
                        <div className="text-blue-600 font-medium">{product.model}</div>
                      </div>
                    </div>

                    {/* Product Details */}
                    <div>
                      <h3 className="text-2xl font-bold text-gray-800 mb-6">{product.name}</h3>
                      
                      {/* Features */}
                      <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-700 mb-3">产品特点</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {product.features.map((feature, idx) => (
                            <div key={idx} className="flex items-center text-gray-600">
                              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                              {feature}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Applications */}
                      <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-700 mb-3">应用领域</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {product.applications.map((app, idx) => (
                            <div key={idx} className="flex items-center text-gray-600">
                              <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                              {app}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Specifications */}
                      <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-700 mb-3">技术参数</h4>
                        <div className="bg-gray-50 rounded-lg p-4">
                          {Object.entries(product.specs).map(([key, value], idx) => (
                            <div key={idx} className="flex justify-between py-1 border-b border-gray-200 last:border-b-0">
                              <span className="text-gray-600">{key}:</span>
                              <span className="font-medium text-gray-800">{value}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex gap-4">
                        <Link
                          href="/contact"
                          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          咨询报价
                        </Link>
                        <Link
                          href="/demo"
                          className="border border-blue-600 text-blue-600 px-6 py-3 rounded-lg hover:bg-blue-50 transition-colors"
                        >
                          3D演示
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
