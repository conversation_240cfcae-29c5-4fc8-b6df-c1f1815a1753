import Layout from '@/components/Layout';
import Breadcrumb from '@/components/Breadcrumb';
import Link from 'next/link';
import ImageWithFallback from '@/components/ImageWithFallback';
import { query } from '@/lib/db';

interface Product {
  id: number;
  name: string;
  model: string;
  slug: string;
  category: string;
  description: string;
  href: string;
  image: string;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 在构建时获取产品数据
async function getProductsData(page: number = 1, limit: number = 10) {
  try {
    const offset = (page - 1) * limit;

    // 查询产品列表
    const products = await query<Product>(
      `SELECT id, name, model, slug, category, description, href, image, sort_order, created_at, updated_at 
       FROM demo_products 
       WHERE is_active = 1 
       ORDER BY sort_order ASC, created_at DESC 
       LIMIT ? OFFSET ?`,
      [limit, offset]
    );

    // 查询总数
    const countResult = await query<{ total: number }>(
      `SELECT COUNT(*) as total FROM demo_products WHERE is_active = 1`
    );

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / limit);

    return {
      products,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('Error fetching products data:', error);
    return {
      products: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      }
    };
  }
}

export default async function ProductsPage() {
  // 在服务端获取数据
  const { products, pagination } = await getProductsData(1, 10);

  const breadcrumbItems = [
    { label: '首页', href: '/' },
    { label: '等离子清洗机设备' }
  ];

  return (
    <Layout>
      <Breadcrumb items={breadcrumbItems} />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              等离子清洗机设备
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed">
              专业设计各类等离子清洗机，满足不同行业需求
            </p>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {products.length > 0 ? (
              <>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                  {products.map((product) => (
                    <div key={product.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                      <div className="h-48 bg-gray-100 relative overflow-hidden border-b border-gray-200">
                        <ImageWithFallback
                          src={product.image || ''}
                          alt={product.name}
                          fill
                          className="object-cover hover:scale-105 transition-transform duration-300"
                          fallbackType="product"
                        />
                      </div>

                      <div className="p-6">
                        <h3 className="text-xl font-bold text-gray-800 mb-2">
                          {product.name}
                        </h3>
                        <p className="text-sm text-blue-600 mb-2">型号：{product.model}</p>
                        <p className="text-gray-600 mb-4">{product.description}</p>

                        <div className="mb-4">
                          <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                            {product.category === 'vacuum' ? '真空等离子' :
                             product.category === 'atmospheric' ? '大气等离子' :
                             product.category === 'medical' ? '医疗专用' :
                             product.category === 'etching' ? '等离子刻蚀' : product.category}
                          </span>
                        </div>

                        <div className="flex space-x-3">
                          <Link
                            href={product.href || `/products/${product.slug}`}
                            className="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded hover:bg-blue-700 transition-colors"
                          >
                            了解详情
                          </Link>
                          <Link
                            href="/demo"
                            className="flex-1 border border-green-600 text-green-600 text-center py-2 px-4 rounded hover:bg-green-600 hover:text-white transition-colors"
                          >
                            3D演示
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Static Pagination Info */}
                {pagination.totalPages > 1 && (
                  <div className="flex justify-center items-center space-x-4">
                    <span className="text-gray-600">
                      第 {pagination.page} 页，共 {pagination.totalPages} 页，总计 {pagination.total} 个产品
                    </span>

                    <div className="flex space-x-2">
                      {pagination.hasPrev && (
                        <Link
                          href={`/products?page=${pagination.page - 1}`}
                          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                        >
                          上一页
                        </Link>
                      )}

                      {pagination.hasNext && (
                        <Link
                          href={`/products?page=${pagination.page + 1}`}
                          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                        >
                          下一页
                        </Link>
                      )}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="flex justify-center items-center py-20">
                <div className="text-center">
                  <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">暂无产品信息</h3>
                  <p className="text-gray-500 mb-6">目前还没有产品信息，我们正在努力完善中</p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Link
                      href="/contact-us"
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                    >
                      联系我们咨询
                    </Link>
                    <Link
                      href="/demo"
                      className="inline-flex items-center px-4 py-2 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors duration-200"
                    >
                      体验3D演示
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>
    </Layout>
  );
}
