'use client';

import Layout from '@/components/Layout';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function LargeVacuumProducts() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const products = [
    {
      name: "PM-2300LNR60LN 大型等离子清洗机",
      model: "PM-2300LNR60LN",
      features: ["大容量处理", "自动化程度高", "多工位设计", "高效率生产"],
      applications: ["汽车零部件", "电子制造", "航空航天", "大批量生产"],
      specs: {
        "腔体尺寸": "600×400×300mm",
        "真空度": "≤0.1Pa",
        "功率": "2000W",
        "气体": "氩气、氧气、氮气、氢气"
      }
    },
    {
      name: "PM/R-80L 真空等离子清洗机",
      model: "PM/R-80L",
      features: ["80升大容量", "PLC控制系统", "多种清洗模式", "远程监控"],
      applications: ["工业清洗", "表面改性", "去胶处理", "材料预处理"],
      specs: {
        "腔体尺寸": "500×350×250mm",
        "真空度": "≤0.05Pa",
        "功率": "1500W",
        "气体": "氩气、氧气、氮气"
      }
    },
    {
      name: "JY-36LN 等离子干刻机",
      model: "JY-36LN",
      features: ["精密刻蚀", "高选择比", "均匀性好", "重复性佳"],
      applications: ["半导体制造", "微电子加工", "MEMS器件", "精密刻蚀"],
      specs: {
        "腔体尺寸": "400×300×200mm",
        "真空度": "≤0.01Pa",
        "功率": "1000W",
        "气体": "CF4、SF6、O2、Ar"
      }
    }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-800 text-white py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className={`mb-8 transition-all duration-1000 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              <span className="inline-block bg-white/20 text-white px-6 py-3 rounded-full text-lg font-medium backdrop-blur-sm border border-white/30">
                产品系列
              </span>
            </div>
            <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-8 transition-all duration-1200 delay-200 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              大型真空等离子清洗机
            </h1>
            <p className={`text-xl md:text-2xl text-purple-100 leading-relaxed max-w-3xl mx-auto transition-all duration-1400 delay-400 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}>
              工业级大批量生产专用等离子清洗设备
            </p>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-1 gap-12">
              {products.map((product, index) => (
                <div 
                  key={index}
                  className={`bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 ${isLoaded ? 'animate-fadeInUp' : 'opacity-0 translate-y-8'}`}
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Product Image */}
                    <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-6xl mb-4">🏭</div>
                        <h3 className="text-2xl font-bold text-gray-800 mb-2">{product.name}</h3>
                        <div className="text-purple-600 font-medium">{product.model}</div>
                      </div>
                    </div>

                    {/* Product Details */}
                    <div>
                      <h3 className="text-2xl font-bold text-gray-800 mb-6">{product.name}</h3>
                      
                      {/* Features */}
                      <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-700 mb-3">产品特点</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {product.features.map((feature, idx) => (
                            <div key={idx} className="flex items-center text-gray-600">
                              <span className="w-2 h-2 bg-purple-400 rounded-full mr-2"></span>
                              {feature}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Applications */}
                      <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-700 mb-3">应用领域</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {product.applications.map((app, idx) => (
                            <div key={idx} className="flex items-center text-gray-600">
                              <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                              {app}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Specifications */}
                      <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-700 mb-3">技术参数</h4>
                        <div className="bg-gray-50 rounded-lg p-4">
                          {Object.entries(product.specs).map(([key, value], idx) => (
                            <div key={idx} className="flex justify-between py-1 border-b border-gray-200 last:border-b-0">
                              <span className="text-gray-600">{key}:</span>
                              <span className="font-medium text-gray-800">{value}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex gap-4">
                        <Link
                          href="/contact"
                          className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
                        >
                          咨询报价
                        </Link>
                        <Link
                          href="/demo"
                          className="border border-purple-600 text-purple-600 px-6 py-3 rounded-lg hover:bg-purple-50 transition-colors"
                        >
                          3D演示
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
