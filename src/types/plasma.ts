// 等离子体技术演示相关类型定义

// 材料类型
export enum MaterialType {
  POLYMER = 'polymer',
  METAL = 'metal',
  GLASS_CERAMIC = 'glass_ceramic'
}

// 工艺类型
export enum ProcessType {
  ACTIVATION = 'activation',
  ETCHING = 'etching',
  COATING = 'coating'
}

// 材料信息
export interface Material {
  id: MaterialType;
  name: string;
  displayName: string;
  description: string;
  color: string;
  roughness: number;
  metalness: number;
  modelPath?: string;
  surfaceProperties?: any;
  applications?: any;
}

// 工艺信息
export interface Process {
  id: ProcessType;
  name: string;
  displayName: string;
  description: string;
  color: string;
  duration: number; // 动画持续时间(秒)
  parameters: ProcessParameters;
  effects?: any;
}

// 工艺参数
export interface ProcessParameters {
  power: number; // 功率 (W)
  gasFlow: number; // 气体流量 (sccm)
  speed: number; // 处理速度 (mm/min)
  gas: string; // 工作气体
  pressure?: number; // 压力 (Torr)
  temperature?: number; // 温度 (°C)
  effectIntensity?: number; // 效果强度 (0-1)
  animationSpeed?: number; // 动画速度倍数
  particleColor?: string; // 粒子颜色
  surfaceChangeRate?: number; // 表面变化率
}

// 动画状态
export enum AnimationState {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed'
}

// 3D场景状态
export interface SceneState {
  selectedMaterial: MaterialType;
  selectedProcess: ProcessType | null;
  animationState: AnimationState;
  showParameters: boolean;
  showComparison: boolean;
  isProcessRunning: boolean;
  processProgress: number; // 0-100
  remainingTime: number; // 剩余时间（秒）
  totalDuration: number; // 总时长（秒）
  processStartTime: number | null; // 开始时间戳
}

// 水滴角测试数据
export interface ContactAngleData {
  before: number; // 处理前接触角
  after: number; // 处理后接触角
  material: MaterialType;
}

// 实时参数显示
export interface RealTimeParameters {
  power: number;
  gasFlow: number;
  speed: number;
  timestamp: number;
}

// 3D模型配置
export interface ModelConfig {
  position: [number, number, number];
  rotation: [number, number, number];
  scale: [number, number, number];
}

// 相机配置
export interface CameraConfig {
  position: [number, number, number];
  target: [number, number, number];
  fov: number;
  near: number;
  far: number;
}

// 光照配置
export interface LightConfig {
  ambient: {
    color: string;
    intensity: number;
  };
  directional: {
    color: string;
    intensity: number;
    position: [number, number, number];
  };
  point?: {
    color: string;
    intensity: number;
    position: [number, number, number];
  };
}

// 粒子系统配置
export interface ParticleConfig {
  count: number;
  color: string;
  size: number;
  speed: number;
  lifetime: number;
  emissionRate: number;
}

// 演示配置
export interface DemoConfig {
  scene: {
    background: string;
    fog?: {
      color: string;
      near: number;
      far: number;
    };
  };
  camera: CameraConfig;
  lights: LightConfig;
  models: {
    workpiece: ModelConfig;
    plasmaGun: ModelConfig;
  };
  particles: {
    plasma: ParticleConfig;
    debris: ParticleConfig;
  };
  animation: {
    defaultDuration: number;
    easing: string;
  };
}

// 用户交互事件
export interface InteractionEvent {
  type: 'material_select' | 'process_start' | 'animation_complete' | 'parameter_change';
  payload: any;
  timestamp: number;
}

// 演示状态管理
export interface DemoState {
  scene: SceneState;
  materials: Material[];
  processes: Process[];
  contactAngles: ContactAngleData[];
  realTimeParams: RealTimeParameters;
  config: DemoConfig;
  isLoading: boolean;
  error: string | null;
}

// 用户交互事件
export interface InteractionEvent {
  type: 'material_select' | 'process_start' | 'animation_complete' | 'parameter_change';
  payload: any;
  timestamp: number;
}
