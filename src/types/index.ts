// Database entity types

export interface Category {
  id: number;
  name: string;
  slug: string;
  type: 'article' | 'product';
  description?: string;
  parent_id?: number;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface Article {
  id: number;
  title: string;
  slug: string;
  excerpt?: string;
  content?: string;
  featured_image?: string;
  category_id?: number;
  status: 'draft' | 'published' | 'archived';
  views: number;
  sort_order: number;
  seo_title?: string;
  seo_description?: string;
  created_at: string;
  updated_at: string;
  published_at?: string;
  category?: Category;
}

export interface Product {
  id: number;
  name: string;
  slug: string;
  model?: string;
  description?: string;
  specifications?: string;
  features?: string;
  applications?: string;
  featured_image?: string;
  gallery?: string[];
  category_id?: number;
  price?: number;
  status: 'active' | 'inactive' | 'discontinued';
  sort_order: number;
  seo_title?: string;
  seo_description?: string;
  created_at: string;
  updated_at: string;
  category?: Category;
}

export interface CompanyInfo {
  id: number;
  section: string;
  title?: string;
  content?: string;
  data?: any;
  updated_at: string;
}

export interface ContactMessage {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
  status: 'new' | 'read' | 'replied' | 'archived';
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export interface Setting {
  id: number;
  key_name: string;
  value?: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  description?: string;
  updated_at: string;
}

// API response types
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Form types
export interface ContactFormData {
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
}

// Navigation types
export interface NavItem {
  label: string;
  href: string;
  children?: NavItem[];
}
