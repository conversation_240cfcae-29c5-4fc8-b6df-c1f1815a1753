// 直接插入演示数据的脚本
const mysql = require('mysql2/promise');

const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'zengtao123',
  database: 'juli_web',
  charset: 'utf8mb4',
  timezone: '+08:00',
};

async function insertDemoData() {
  let connection = null;
  
  try {
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 清空现有数据
    console.log('🗑️ 清空现有数据...');
    await connection.execute('DELETE FROM demo_materials');
    await connection.execute('DELETE FROM demo_processes');
    await connection.execute('DELETE FROM demo_products');
    await connection.execute('DELETE FROM demo_news');
    
    // 插入材料数据
    console.log('📦 插入材料数据...');
    const materials = [
      {
        id: 'polymer',
        name: 'polymer',
        display_name: '聚合物',
        description: 'PP、PC等塑料材料',
        color: '#4CAF50',
        roughness: 0.1,
        metalness: 0,
        model_path: '/models/polymer_sample.glb',
        surface_properties: JSON.stringify({
          contactAngle: { before: 95, after: 25 },
          surfaceEnergy: { before: 32, after: 68 },
          roughnessChange: { before: 0.1, after: 0.3 }
        }),
        applications: JSON.stringify(['汽车零件', '电子外壳', '包装材料', '医疗器械'])
      },
      {
        id: 'metal',
        name: 'metal',
        display_name: '金属',
        description: '铝、不锈钢等金属',
        color: '#9E9E9E',
        roughness: 0.3,
        metalness: 1,
        model_path: '/models/metal_sample.glb',
        surface_properties: JSON.stringify({
          contactAngle: { before: 85, after: 15 },
          surfaceEnergy: { before: 45, after: 72 },
          roughnessChange: { before: 0.3, after: 0.5 }
        }),
        applications: JSON.stringify(['航空航天', '汽车工业', '电子设备', '医疗植入物'])
      },
      {
        id: 'glass_ceramic',
        name: 'glass_ceramic',
        display_name: '玻璃陶瓷',
        description: '玻璃、陶瓷等无机材料',
        color: '#2196F3',
        roughness: 0.1,
        metalness: 0,
        model_path: '/models/glass_sample.glb',
        surface_properties: JSON.stringify({
          contactAngle: { before: 75, after: 8 },
          surfaceEnergy: { before: 38, after: 65 },
          roughnessChange: { before: 0.1, after: 0.25 }
        }),
        applications: JSON.stringify(['光学器件', '电子基板', '生物医学', '装饰材料'])
      }
    ];

    for (const material of materials) {
      await connection.execute(
        `INSERT INTO demo_materials (
          id, name, display_name, description, color, roughness, metalness,
          model_path, surface_properties, applications
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          material.id, material.name, material.display_name, material.description,
          material.color, material.roughness, material.metalness, material.model_path,
          material.surface_properties, material.applications
        ]
      );
    }
    console.log(`✅ 插入了 ${materials.length} 个材料`);

    // 插入工艺数据
    console.log('⚙️ 插入工艺数据...');
    const processes = [
      {
        id: 'activation',
        name: 'activation',
        display_name: '表面活化',
        description: '提高表面能，改善润湿性和粘接性能',
        color: '#9C27B0',
        duration: 30,
        parameters: JSON.stringify({
          power: 100,
          gasFlow: 50,
          speed: 10,
          gas: 'O₂/Ar',
          pressure: 0.5,
          temperature: 25
        }),
        effects: JSON.stringify(['提高表面能', '改善润湿性', '增强粘接力'])
      },
      {
        id: 'etching',
        name: 'etching',
        display_name: '等离子刻蚀',
        description: '去除氧化层和污染物，增加表面粗糙度',
        color: '#FF5722',
        duration: 45,
        parameters: JSON.stringify({
          power: 150,
          gasFlow: 80,
          speed: 8,
          gas: 'Ar/CF₄',
          pressure: 0.8,
          temperature: 35
        }),
        effects: JSON.stringify(['去除污染物', '增加粗糙度', '提高附着力'])
      },
      {
        id: 'coating',
        name: 'coating',
        display_name: '等离子涂层',
        description: '在表面沉积功能性薄膜层',
        color: '#00BCD4',
        duration: 60,
        parameters: JSON.stringify({
          power: 120,
          gasFlow: 60,
          speed: 6,
          gas: 'SiH₄/N₂',
          pressure: 1.2,
          temperature: 45
        }),
        effects: JSON.stringify(['沉积薄膜', '改善性能', '增加功能性'])
      }
    ];

    for (const process of processes) {
      await connection.execute(
        `INSERT INTO demo_processes (
          id, name, display_name, description, color, duration, parameters, effects
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          process.id, process.name, process.display_name, process.description,
          process.color, process.duration, process.parameters, process.effects
        ]
      );
    }
    console.log(`✅ 插入了 ${processes.length} 个工艺`);

    // 插入产品数据
    console.log('🛠️ 插入产品数据...');
    const products = [
      { name: '小型等离子清洗机', model: 'PM-20LN', slug: 'small-vacuum', category: 'vacuum', description: '适用于小批量样品处理的真空等离子清洗设备', href: '/products/small-vacuum', image: '/images/products/pm-20ln.jpg', sort_order: 1 },
      { name: '大型等离子清洗机', model: 'PM-2300LNR60LN', slug: 'large-vacuum', category: 'vacuum', description: '适用于大批量生产的真空等离子清洗设备', href: '/products/large-vacuum', image: '/images/products/pm-2300.jpg', sort_order: 2 },
      { name: '医疗导管等离子清洗机', model: 'PM-210LN', slug: 'medical-catheter', category: 'medical', description: '专为医疗导管等医疗器械设计的等离子清洗设备', href: '/products/large-vacuum', image: '/images/products/pm-210ln.jpg', sort_order: 3 },
      { name: '等离子干刻机', model: 'JY-36LN', slug: 'dry-etching', category: 'etching', description: '专业的等离子干刻设备，适用于微电子加工', href: '/products/large-vacuum', image: '/images/products/jy-36ln.jpg', sort_order: 4 },
      { name: '小型等离子清洗机', model: 'PM-3LN', slug: 'mini-vacuum', category: 'vacuum', description: '超小型等离子清洗设备，适合桌面使用', href: '/products/small-vacuum', image: '/images/products/pm-3ln.jpg', sort_order: 5 },
      { name: '大气等离子清洗设备', model: 'AP-PM1000', slug: 'atmospheric', category: 'atmospheric', description: '常压下工作的等离子清洗设备，无需真空系统', href: '/products/atmospheric', image: '/images/products/ap-pm1000.jpg', sort_order: 6 },
      { name: '薄膜等离子清洗机', model: 'AP-800-AJR', slug: 'film-atmospheric', category: 'atmospheric', description: '专为薄膜材料设计的大气等离子清洗设备', href: '/products/atmospheric', image: '/images/products/ap-800.jpg', sort_order: 7 },
      { name: '真空等离子清洗机', model: 'PM/R-80L', slug: 'vacuum-80l', category: 'vacuum', description: '大容量真空等离子清洗设备，适用于批量处理', href: '/products/large-vacuum', image: '/images/products/pm-r-80l.jpg', sort_order: 8 }
    ];

    for (const product of products) {
      await connection.execute(
        `INSERT INTO demo_products (name, model, slug, category, description, href, image, sort_order) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [product.name, product.model, product.slug, product.category, product.description, product.href, product.image, product.sort_order]
      );
    }
    console.log(`✅ 插入了 ${products.length} 个产品`);

    // 插入新闻数据
    console.log('📰 插入新闻数据...');
    const news = [
      { title: '微流控PDMS芯片键合等离子清洗机应用-微流控pdms芯片键', excerpt: '微流控PDMS芯片键合等离子清洗机是一种用于清洗微流控PDMS芯片的设备，该设备在微流控技术和等离子清洗技术的...', category: 'applications', href: '/news/applications', publish_date: '2025-07-24', sort_order: 1 },
      { title: '等离子外加工清洗机、等离子外加工清洗机：高效清洗新选择', excerpt: '随着现代工业的不断发展，清洗工艺也得到了越来越多的关注。传统的清洗方法可能存在对环境的污染、清洗效率低下等问题...', category: 'applications', href: '/news/applications', publish_date: '2025-07-24', sort_order: 2 },
      { title: '改善聚丙烯腈PAN润湿性和粘接性 改善材料表面的性能(改善聚', excerpt: '随着科学技术的不断发展，材料科学领域也在不断进步。在材料研究中，表面性能的改善一直是研究的重点之一。本文将围绕...', category: 'applications', href: '/news/applications', publish_date: '2025-07-24', sort_order: 3 },
      { title: '等离子除胶处理机使用方法—等离子除胶处理机使用指南', excerpt: '等离子除胶处理机使用指南你是否曾经遇到过这样的问题：胶水在工业生产过程中难以去除，导致产品质量下降，甚至无法正...', category: 'applications', href: '/news/applications', publish_date: '2025-07-24', sort_order: 4 }
    ];

    for (const article of news) {
      await connection.execute(
        `INSERT INTO demo_news (title, excerpt, category, href, publish_date, sort_order) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [article.title, article.excerpt, article.category, article.href, article.publish_date, article.sort_order]
      );
    }
    console.log(`✅ 插入了 ${news.length} 条新闻`);

    // 验证数据
    console.log('🔍 验证数据...');
    const [materialsCount] = await connection.execute('SELECT COUNT(*) as count FROM demo_materials');
    const [processesCount] = await connection.execute('SELECT COUNT(*) as count FROM demo_processes');
    const [productsCount] = await connection.execute('SELECT COUNT(*) as count FROM demo_products');
    const [newsCount] = await connection.execute('SELECT COUNT(*) as count FROM demo_news');
    const [configsCount] = await connection.execute('SELECT COUNT(*) as count FROM demo_configs');
    
    console.log(`📊 最终数据统计:`);
    console.log(`   - 材料数据: ${materialsCount[0].count} 条`);
    console.log(`   - 工艺数据: ${processesCount[0].count} 条`);
    console.log(`   - 产品数据: ${productsCount[0].count} 条`);
    console.log(`   - 新闻数据: ${newsCount[0].count} 条`);
    console.log(`   - 配置数据: ${configsCount[0].count} 条`);
    
    console.log('🎉 演示数据插入完成！');
    
  } catch (error) {
    console.error('❌ 插入失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行插入
insertDemoData();
