// 演示数据初始化脚本 (JavaScript版本)
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'zengtao123',
  database: 'juli_web',
  charset: 'utf8mb4',
  timezone: '+08:00',
  multipleStatements: true,
};

async function initDemoData() {
  let connection = null;
  
  try {
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    
    console.log('✅ 数据库连接成功');
    
    // 读取SQL文件
    const sqlFilePath = path.join(process.cwd(), 'database', 'demo-data.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('📄 读取SQL文件成功');
    
    // 分割SQL语句并执行
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`🚀 开始执行 ${sqlStatements.length} 条SQL语句...`);
    
    for (let i = 0; i < sqlStatements.length; i++) {
      const stmt = sqlStatements[i];
      if (stmt) {
        try {
          await connection.execute(stmt);
          if (i % 10 === 0) {
            console.log(`   执行进度: ${i + 1}/${sqlStatements.length}`);
          }
        } catch (error) {
          console.warn(`   警告: SQL语句执行失败 (${i + 1}): ${error.message}`);
        }
      }
    }
    
    console.log('✅ 演示数据初始化完成！');
    
    // 验证数据
    console.log('🔍 验证数据...');
    
    const [materials] = await connection.execute('SELECT COUNT(*) as count FROM demo_materials');
    const [processes] = await connection.execute('SELECT COUNT(*) as count FROM demo_processes');
    const [products] = await connection.execute('SELECT COUNT(*) as count FROM demo_products');
    const [news] = await connection.execute('SELECT COUNT(*) as count FROM demo_news');
    
    console.log(`📊 数据统计:`);
    console.log(`   - 材料数据: ${materials[0].count} 条`);
    console.log(`   - 工艺数据: ${processes[0].count} 条`);
    console.log(`   - 产品数据: ${products[0].count} 条`);
    console.log(`   - 新闻数据: ${news[0].count} 条`);
    
    // 插入配置数据
    console.log('⚙️ 插入配置数据...');
    await insertConfigData(connection);
    
    console.log('🎉 所有数据初始化完成！');
    
  } catch (error) {
    console.error('❌ 初始化失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

async function insertConfigData(connection) {
  // 动画配置
  const animationConfig = {
    durations: {
      materialSwitch: 1000,
      processStart: 800,
      processComplete: 1200,
      particleLife: 2000,
      surfaceChange: 1500
    },
    timings: {
      ease: "cubic-bezier(0.4, 0, 0.2, 1)",
      bounce: "cubic-bezier(0.68, -0.55, 0.265, 1.55)",
      smooth: "cubic-bezier(0.25, 0.46, 0.45, 0.94)"
    }
  };

  // 场景配置
  const sceneConfig = {
    camera: {
      position: { x: 0, y: 5, z: 10 },
      fov: 75,
      near: 0.1,
      far: 1000
    },
    lighting: {
      ambient: { color: "#404040", intensity: 0.4 },
      directional: { color: "#ffffff", intensity: 1, position: { x: 10, y: 10, z: 5 } },
      point: { color: "#9C27B0", intensity: 0.8, position: { x: 0, y: 3, z: 0 } }
    },
    background: "#1a1a2e"
  };

  // 性能配置
  const performanceConfig = {
    targetFPS: 60,
    maxParticles: 1000,
    enableShadows: true,
    enableAntialiasing: true,
    pixelRatio: 2
  };

  // UI配置
  const uiConfig = {
    floatingPanels: {
      zIndex: 40,
      backdropBlur: "6px",
      borderRadius: "6px",
      padding: "4px 6px"
    },
    colors: {
      primary: "#9C27B0",
      secondary: "#4CAF50",
      accent: "#FF5722",
      background: "rgba(0, 0, 0, 0.25)",
      text: "#ffffff",
      textSecondary: "#b0b0b0"
    },
    breakpoints: {
      mobile: 768,
      tablet: 1024,
      desktop: 1200
    }
  };

  // 参数范围配置
  const parameterRangesConfig = {
    power: { min: 50, max: 200, unit: "W" },
    gasFlow: { min: 20, max: 120, unit: "sccm" },
    speed: { min: 5, max: 15, unit: "mm/min" },
    pressure: { min: 0.1, max: 2, unit: "Pa" },
    temperature: { min: 15, max: 60, unit: "°C" }
  };

  // 主题颜色配置
  const themeColorsConfig = {
    materials: {
      polymer: "#4CAF50",
      metal: "#9E9E9E",
      glass_ceramic: "#2196F3"
    },
    processes: {
      activation: "#9C27B0",
      etching: "#FF5722",
      coating: "#00BCD4"
    },
    status: {
      running: "#4CAF50",
      paused: "#FFC107",
      completed: "#2196F3",
      error: "#F44336"
    }
  };

  // 键盘快捷键配置
  const keyboardShortcutsConfig = {
    startDemo: "Space",
    pauseResume: "P",
    stop: "S",
    reset: "R",
    fullscreen: "F",
    help: "H",
    nextMaterial: "ArrowRight",
    prevMaterial: "ArrowLeft",
    nextProcess: "ArrowDown",
    prevProcess: "ArrowUp"
  };

  // 技术信息配置
  const technicalInfoConfig = {
    activation: {
      title: "表面活化",
      description: "通过等离子体处理提高材料表面能，改善润湿性和粘接性能。",
      principle: "等离子体中的活性粒子与材料表面发生化学反应，引入极性基团，增加表面能。",
      applications: ["粘接前处理", "涂装前处理", "印刷前处理", "生物相容性改善"]
    },
    etching: {
      title: "刻蚀",
      description: "去除表面污染物和氧化层，增加表面粗糙度，提高附着力。",
      principle: "等离子体中的离子轰击材料表面，物理和化学作用同时进行，去除表面层。",
      applications: ["清洁处理", "粗化处理", "去胶处理", "微加工"]
    },
    coating: {
      title: "镀膜",
      description: "在材料表面沉积功能性薄膜，赋予特殊性能。",
      principle: "等离子体激活前驱体分子，在基材表面发生聚合反应形成薄膜。",
      applications: ["防腐涂层", "绝缘涂层", "生物涂层", "光学涂层"]
    }
  };

  // 应用领域配置
  const applicationFieldsConfig = [
    { icon: "🚗", name: "汽车工业", description: "汽车零部件表面处理，提高粘接和涂装质量" },
    { icon: "📱", name: "电子产品", description: "电子器件清洁和表面改性，提高可靠性" },
    { icon: "🏥", name: "医疗器械", description: "医疗器械表面处理，改善生物相容性" },
    { icon: "✈️", name: "航空航天", description: "航空航天材料表面处理，提高性能" },
    { icon: "📦", name: "包装材料", description: "包装材料表面改性，提高印刷和粘接性能" },
    { icon: "🔬", name: "科研实验", description: "科研实验中的样品预处理和表面改性" }
  ];

  // 插入配置数据
  const configs = [
    { key: 'animation', value: animationConfig, description: '动画配置' },
    { key: 'scene', value: sceneConfig, description: '场景配置' },
    { key: 'performance', value: performanceConfig, description: '性能配置' },
    { key: 'ui', value: uiConfig, description: 'UI配置' },
    { key: 'parameterRanges', value: parameterRangesConfig, description: '参数范围配置' },
    { key: 'themeColors', value: themeColorsConfig, description: '主题颜色配置' },
    { key: 'keyboardShortcuts', value: keyboardShortcutsConfig, description: '键盘快捷键配置' },
    { key: 'technicalInfo', value: technicalInfoConfig, description: '技术信息配置' },
    { key: 'applicationFields', value: applicationFieldsConfig, description: '应用领域配置' }
  ];

  for (const config of configs) {
    await connection.execute(
      `INSERT INTO demo_configs (config_key, config_value, description) 
       VALUES (?, ?, ?) 
       ON DUPLICATE KEY UPDATE 
         config_value = VALUES(config_value),
         description = VALUES(description),
         updated_at = CURRENT_TIMESTAMP`,
      [config.key, JSON.stringify(config.value), config.description]
    );
  }

  console.log(`✅ 插入了 ${configs.length} 个配置项`);
}

// 运行初始化
initDemoData();
