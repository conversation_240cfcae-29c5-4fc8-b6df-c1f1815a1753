import { test, expect } from '@playwright/test';

test.describe('基本UI功能测试', () => {
  test('登录页面基本功能', async ({ page }) => {
    await page.goto('/admin');
    
    // 检查登录页面元素
    await expect(page.locator('text=管理后台登录')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // 执行登录
    await page.locator('input[type="password"]').fill('admin123');
    await page.locator('button[type="submit"]').click();
    
    // 验证登录成功
    await expect(page).toHaveURL('/admin');
    await expect(page.locator('text=仪表盘')).toBeVisible();
  });

  test('仪表盘基本元素', async ({ page }) => {
    // 先登录
    await page.goto('/admin');
    await page.locator('input[type="password"]').fill('admin123');
    await page.locator('button[type="submit"]').click();
    await page.waitForURL('/admin');
    
    // 检查仪表盘元素
    await expect(page.locator('text=仪表盘')).toBeVisible();
    await expect(page.locator('text=产品总数')).toBeVisible();
    await expect(page.locator('text=新闻总数')).toBeVisible();
  });

  test('导航菜单功能', async ({ page }) => {
    // 先登录
    await page.goto('/admin');
    await page.locator('input[type="password"]').fill('admin123');
    await page.locator('button[type="submit"]').click();
    await page.waitForURL('/admin');
    
    // 测试产品管理导航
    await page.locator('text=产品管理').click();
    await expect(page).toHaveURL(/.*products/);
    await expect(page.locator('text=产品管理')).toBeVisible();
    
    // 测试新闻管理导航
    await page.locator('text=新闻管理').click();
    await expect(page).toHaveURL(/.*news/);
    await expect(page.locator('text=新闻管理')).toBeVisible();
    
    // 返回仪表盘
    await page.locator('text=仪表盘').click();
    await expect(page).toHaveURL('/admin');
  });
});
