import { test, expect } from '@playwright/test';

test.describe('仪表盘UI测试', () => {
  test.beforeEach(async ({ page }) => {
    // 先登录
    await page.goto('/admin');
    await page.locator('input[type="password"]').fill('admin123');
    await page.locator('button[type="submit"]').click();
    await page.waitForURL('/admin');
  });

  test('仪表盘布局和基本元素', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('text=仪表盘')).toBeVisible();
    
    // 检查欢迎信息
    await expect(page.locator('text=欢迎使用等离子清洗专家管理系统')).toBeVisible();
    
    // 检查统计卡片
    await expect(page.locator('text=产品总数')).toBeVisible();
    await expect(page.locator('text=新闻总数')).toBeVisible();
    await expect(page.locator('text=启用产品')).toBeVisible();
    await expect(page.locator('text=已发布新闻')).toBeVisible();
    
    // 检查快速操作区域
    await expect(page.locator('text=快速操作')).toBeVisible();
    
    // 检查最近活动区域
    await expect(page.locator('text=最近活动')).toBeVisible();
  });

  test('统计卡片视觉效果测试', async ({ page }) => {
    const statisticCards = page.locator('.ant-statistic-card');
    const cardCount = await statisticCards.count();
    
    // 应该有4个统计卡片
    expect(cardCount).toBe(4);
    
    // 检查每个卡片的渐变背景
    for (let i = 0; i < cardCount; i++) {
      const card = statisticCards.nth(i);
      await expect(card).toBeVisible();
      
      const cardStyles = await card.evaluate(el => getComputedStyle(el.parentElement));
      expect(cardStyles.background).toContain('gradient');
    }
    
    // 检查卡片悬停效果
    const firstCard = statisticCards.first();
    await firstCard.hover();
    await page.waitForTimeout(500);
    
    const cardStyles = await firstCard.evaluate(el => getComputedStyle(el.parentElement));
    expect(cardStyles.transform).toContain('translateY');
  });

  test('快速操作区域交互测试', async ({ page }) => {
    // 检查快速操作按钮
    const quickActions = page.locator('text=快速操作').locator('..').locator('div').filter({ hasText: /添加产品|发布新闻|查看统计|系统设置/ });
    
    // 测试添加产品按钮
    const addProductButton = page.locator('text=添加产品').first();
    if (await addProductButton.isVisible()) {
      await addProductButton.click();
      // 应该跳转到产品管理页面或打开产品添加模态框
      await page.waitForTimeout(1000);
    }
    
    // 返回仪表盘
    await page.goto('/admin');
    
    // 测试发布新闻按钮
    const addNewsButton = page.locator('text=发布新闻').first();
    if (await addNewsButton.isVisible()) {
      await addNewsButton.click();
      await page.waitForTimeout(1000);
    }
  });

  test('导航菜单测试', async ({ page }) => {
    // 检查侧边栏菜单
    await expect(page.locator('text=仪表盘')).toBeVisible();
    await expect(page.locator('text=产品管理')).toBeVisible();
    await expect(page.locator('text=新闻管理')).toBeVisible();
    
    // 测试菜单导航
    await page.locator('text=产品管理').click();
    await expect(page).toHaveURL(/.*products/);
    
    await page.locator('text=新闻管理').click();
    await expect(page).toHaveURL(/.*news/);
    
    await page.locator('text=仪表盘').click();
    await expect(page).toHaveURL('/admin');
  });

  test('响应式设计测试', async ({ page }) => {
    // 桌面端测试
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('text=仪表盘')).toBeVisible();
    
    // 检查统计卡片在桌面端的布局
    const statisticCards = page.locator('.ant-statistic-card');
    const firstCardBox = await statisticCards.first().boundingBox();
    const secondCardBox = await statisticCards.nth(1).boundingBox();
    
    // 在桌面端，卡片应该水平排列
    if (firstCardBox && secondCardBox) {
      expect(Math.abs(firstCardBox.y - secondCardBox.y)).toBeLessThan(50);
    }
    
    // 平板端测试
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500);
    await expect(page.locator('text=仪表盘')).toBeVisible();
    
    // 手机端测试
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    await expect(page.locator('text=仪表盘')).toBeVisible();
    
    // 检查手机端菜单是否收起
    const siderMenu = page.locator('.ant-pro-layout-sider');
    if (await siderMenu.isVisible()) {
      const siderWidth = await siderMenu.evaluate(el => el.offsetWidth);
      expect(siderWidth).toBeLessThan(100); // 手机端应该收起
    }
  });

  test('用户头像和操作菜单测试', async ({ page }) => {
    // 检查用户头像
    const userAvatar = page.locator('.ant-avatar');
    await expect(userAvatar).toBeVisible();
    
    // 点击用户头像打开下拉菜单
    await userAvatar.click();
    await page.waitForTimeout(500);
    
    // 检查下拉菜单项
    await expect(page.locator('text=返回网站')).toBeVisible();
    await expect(page.locator('text=退出登录')).toBeVisible();
    
    // 测试返回网站链接
    const homeLink = page.locator('text=返回网站');
    await expect(homeLink).toBeVisible();
    
    // 关闭下拉菜单
    await page.keyboard.press('Escape');
  });

  test('数据加载和显示测试', async ({ page }) => {
    // 检查统计数据是否正确显示
    const productCount = page.locator('text=产品总数').locator('..').locator('.ant-statistic-content-value');
    const newsCount = page.locator('text=新闻总数').locator('..').locator('.ant-statistic-content-value');
    
    await expect(productCount).toBeVisible();
    await expect(newsCount).toBeVisible();
    
    // 检查数据是否为数字
    const productValue = await productCount.textContent();
    const newsValue = await newsCount.textContent();
    
    expect(productValue).toMatch(/^\d+$/);
    expect(newsValue).toMatch(/^\d+$/);
  });

  test('页面性能测试', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/admin');
    await page.locator('input[type="password"]').fill('admin123');
    await page.locator('button[type="submit"]').click();
    
    // 等待仪表盘完全加载
    await page.locator('text=仪表盘').waitFor();
    await page.locator('text=产品总数').waitFor();
    
    const loadTime = Date.now() - startTime;
    
    // 仪表盘应该在3秒内加载完成
    expect(loadTime).toBeLessThan(3000);
  });

  test('颜色对比度和可读性测试', async ({ page }) => {
    // 检查主要文本的颜色对比度
    const titleElement = page.locator('text=仪表盘').first();
    const titleStyles = await titleElement.evaluate(el => getComputedStyle(el));
    
    // 检查文本颜色不是透明或过浅
    expect(titleStyles.color).not.toBe('rgba(0, 0, 0, 0)');
    expect(titleStyles.color).not.toBe('transparent');
    
    // 检查统计卡片的文本可读性
    const statisticValue = page.locator('.ant-statistic-content-value').first();
    const valueStyles = await statisticValue.evaluate(el => getComputedStyle(el));
    
    expect(valueStyles.fontSize).not.toBe('0px');
    expect(parseInt(valueStyles.fontSize)).toBeGreaterThan(12);
  });

  test('动画和过渡效果测试', async ({ page }) => {
    // 测试卡片悬停动画
    const firstCard = page.locator('.ant-statistic-card').first();
    
    // 获取初始位置
    const initialBox = await firstCard.boundingBox();
    
    // 悬停
    await firstCard.hover();
    await page.waitForTimeout(500);
    
    // 获取悬停后位置
    const hoverBox = await firstCard.boundingBox();
    
    // 检查是否有位置变化（上浮效果）
    if (initialBox && hoverBox) {
      expect(hoverBox.y).toBeLessThan(initialBox.y);
    }
    
    // 移开鼠标
    await page.locator('body').hover();
    await page.waitForTimeout(500);
    
    // 检查是否恢复原位
    const finalBox = await firstCard.boundingBox();
    if (initialBox && finalBox) {
      expect(Math.abs(finalBox.y - initialBox.y)).toBeLessThan(5);
    }
  });
});
