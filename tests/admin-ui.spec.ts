import { test, expect } from '@playwright/test';

test.describe('等离子清洗专家管理后台 UI测试', () => {
  
  test.beforeEach(async ({ page }) => {
    await page.goto('/admin');
  });

  test.describe('登录页面测试', () => {
    test('登录页面布局和样式', async ({ page }) => {
      // 检查页面标题
      await expect(page).toHaveTitle(/等离子清洗专家/);
      
      // 检查登录表单元素
      await expect(page.locator('text=管理后台登录')).toBeVisible();
      await expect(page.locator('text=等离子清洗专家 - 管理系统')).toBeVisible();
      
      // 检查密码输入框
      const passwordInput = page.locator('input[type="password"]');
      await expect(passwordInput).toBeVisible();
      await expect(passwordInput).toHaveAttribute('placeholder', '请输入管理密码');
      
      // 检查登录按钮
      const loginButton = page.locator('button[type="submit"]');
      await expect(loginButton).toBeVisible();
      await expect(loginButton).toContainText('登录系统');
      
      // 检查返回网站按钮
      await expect(page.locator('text=返回网站首页')).toBeVisible();
      
      // 检查演示说明
      await expect(page.locator('text=演示说明')).toBeVisible();
      await expect(page.locator('text=admin123')).toBeVisible();
    });

    test('登录页面响应式设计', async ({ page }) => {
      // 测试桌面端
      await page.setViewportSize({ width: 1920, height: 1080 });
      await expect(page.locator('text=管理后台登录')).toBeVisible();
      
      // 测试平板端
      await page.setViewportSize({ width: 768, height: 1024 });
      await expect(page.locator('text=管理后台登录')).toBeVisible();
      
      // 测试移动端
      await page.setViewportSize({ width: 375, height: 667 });
      await expect(page.locator('text=管理后台登录')).toBeVisible();
    });

    test('登录功能测试', async ({ page }) => {
      // 输入错误密码
      await page.fill('input[type="password"]', 'wrongpassword');
      await page.click('button[type="submit"]');
      await expect(page.locator('text=密码错误')).toBeVisible();
      
      // 输入正确密码
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');
      
      // 等待跳转到仪表盘
      await expect(page).toHaveURL(/.*\/admin$/);
      await expect(page.locator('text=仪表盘')).toBeVisible();
    });
  });

  test.describe('仪表盘测试', () => {
    test.beforeEach(async ({ page }) => {
      // 先登录
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/admin$/);
    });

    test('仪表盘布局和统计卡片', async ({ page }) => {
      // 检查页面标题
      await expect(page.locator('text=仪表盘')).toBeVisible();
      await expect(page.locator('text=欢迎使用等离子清洗专家管理系统')).toBeVisible();
      
      // 检查统计卡片
      await expect(page.locator('text=产品总数')).toBeVisible();
      await expect(page.locator('text=新闻总数')).toBeVisible();
      await expect(page.locator('text=启用产品')).toBeVisible();
      await expect(page.locator('text=已发布新闻')).toBeVisible();
      
      // 检查快速操作
      await expect(page.locator('text=快速操作')).toBeVisible();
      await expect(page.locator('text=添加产品')).toBeVisible();
      await expect(page.locator('text=发布新闻')).toBeVisible();
      
      // 检查最近活动
      await expect(page.locator('text=最近活动')).toBeVisible();
    });

    test('快速操作功能', async ({ page }) => {
      // 点击添加产品
      await page.click('text=添加产品');
      await expect(page).toHaveURL(/.*\/admin\/products$/);
      
      // 返回仪表盘
      await page.goto('/admin');
      
      // 点击发布新闻
      await page.click('text=发布新闻');
      await expect(page).toHaveURL(/.*\/admin\/news$/);
    });
  });

  test.describe('产品管理页面测试', () => {
    test.beforeEach(async ({ page }) => {
      // 先登录
      await page.goto('/admin');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/admin$/);
      await page.goto('/admin/products');
    });

    test('产品管理页面布局', async ({ page }) => {
      // 检查页面标题
      await expect(page.locator('text=产品管理')).toBeVisible();
      
      // 检查添加产品按钮
      await expect(page.locator('text=添加产品')).toBeVisible();
      
      // 检查搜索表单
      await expect(page.locator('input[placeholder*="产品名称"]')).toBeVisible();
      
      // 检查表格列标题
      await expect(page.locator('text=产品名称')).toBeVisible();
      await expect(page.locator('text=类别')).toBeVisible();
      await expect(page.locator('text=状态')).toBeVisible();
      await expect(page.locator('text=操作')).toBeVisible();
    });

    test('产品添加表单测试', async ({ page }) => {
      // 点击添加产品按钮
      await page.click('text=添加产品');
      
      // 检查模态框
      await expect(page.locator('text=添加产品')).toBeVisible();
      
      // 检查表单字段
      await expect(page.locator('input[placeholder="请输入产品名称"]')).toBeVisible();
      await expect(page.locator('input[placeholder="请输入产品型号"]')).toBeVisible();
      await expect(page.locator('text=产品类别')).toBeVisible();
      await expect(page.locator('textarea[placeholder="请输入产品描述"]')).toBeVisible();
      
      // 测试表单验证
      await page.click('text=创建');
      await expect(page.locator('text=请输入产品名称')).toBeVisible();
      
      // 关闭模态框
      await page.press('body', 'Escape');
    });
  });

  test.describe('新闻管理页面测试', () => {
    test.beforeEach(async ({ page }) => {
      // 先登录
      await page.goto('/admin');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/admin$/);
      await page.goto('/admin/news');
    });

    test('新闻管理页面布局', async ({ page }) => {
      // 检查页面标题
      await expect(page.locator('text=新闻管理')).toBeVisible();
      
      // 检查发布新闻按钮
      await expect(page.locator('text=发布新闻')).toBeVisible();
      
      // 检查搜索表单
      await expect(page.locator('input[placeholder*="新闻标题"]')).toBeVisible();
      
      // 检查表格列标题
      await expect(page.locator('text=新闻标题')).toBeVisible();
      await expect(page.locator('text=类别')).toBeVisible();
      await expect(page.locator('text=状态')).toBeVisible();
      await expect(page.locator('text=操作')).toBeVisible();
    });

    test('新闻发布表单测试', async ({ page }) => {
      // 点击发布新闻按钮
      await page.click('text=发布新闻');
      
      // 检查模态框
      await expect(page.locator('text=发布新闻')).toBeVisible();
      
      // 检查表单字段
      await expect(page.locator('input[placeholder="请输入新闻标题"]')).toBeVisible();
      await expect(page.locator('textarea[placeholder="请输入新闻摘要"]')).toBeVisible();
      await expect(page.locator('textarea[placeholder="请输入新闻内容"]')).toBeVisible();
      
      // 测试表单验证
      await page.click('text=发布');
      await expect(page.locator('text=请输入新闻标题')).toBeVisible();
      
      // 关闭模态框
      await page.press('body', 'Escape');
    });
  });

  test.describe('响应式设计测试', () => {
    test.beforeEach(async ({ page }) => {
      // 先登录
      await page.goto('/admin');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/admin$/);
    });

    test('桌面端布局', async ({ page }) => {
      await page.setViewportSize({ width: 1920, height: 1080 });
      
      // 检查侧边栏
      await expect(page.locator('text=产品管理')).toBeVisible();
      await expect(page.locator('text=新闻管理')).toBeVisible();
      
      // 检查统计卡片布局
      const cards = page.locator('[class*="ant-statistic"]');
      await expect(cards).toHaveCount(4);
    });

    test('平板端布局', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // 检查布局适配
      await expect(page.locator('text=仪表盘')).toBeVisible();
      
      // 检查统计卡片在平板端的显示
      const cards = page.locator('[class*="ant-statistic"]');
      await expect(cards).toHaveCount(4);
    });

    test('移动端布局', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      
      // 检查移动端布局
      await expect(page.locator('text=仪表盘')).toBeVisible();
      
      // 检查移动端菜单
      // 在移动端，侧边栏可能会被隐藏或折叠
    });
  });

  test.describe('交互和可用性测试', () => {
    test.beforeEach(async ({ page }) => {
      // 先登录
      await page.goto('/admin');
      await page.fill('input[type="password"]', 'admin123');
      await page.click('button[type="submit"]');
      await page.waitForURL(/.*\/admin$/);
    });

    test('按钮点击区域测试', async ({ page }) => {
      // 测试统计卡片的点击区域
      const productCard = page.locator('text=产品总数').locator('..');
      await expect(productCard).toBeVisible();
      
      // 测试快速操作按钮
      const addProductBtn = page.locator('text=添加产品');
      await expect(addProductBtn).toBeVisible();
      
      // 检查按钮的最小点击区域（44px x 44px）
      const buttonBox = await addProductBtn.boundingBox();
      expect(buttonBox?.height).toBeGreaterThanOrEqual(44);
    });

    test('键盘导航测试', async ({ page }) => {
      // 测试Tab键导航
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // 检查焦点状态
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
    });

    test('颜色对比度测试', async ({ page }) => {
      // 检查主要文本的可读性
      const titleElement = page.locator('text=仪表盘');
      await expect(titleElement).toBeVisible();
      
      // 检查按钮文本的可读性
      const buttonElement = page.locator('text=添加产品');
      await expect(buttonElement).toBeVisible();
    });
  });
});
