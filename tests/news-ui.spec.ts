import { test, expect } from '@playwright/test';

test.describe('新闻管理页面UI测试', () => {
  test.beforeEach(async ({ page }) => {
    // 先登录并导航到新闻管理页面
    await page.goto('/admin');
    await page.locator('input[type="password"]').fill('admin123');
    await page.locator('button[type="submit"]').click();
    await page.waitForURL('/admin');
    await page.locator('text=新闻管理').click();
    await page.waitForURL(/.*news/);
  });

  test('新闻管理页面基本布局', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('text=新闻管理')).toBeVisible();
    
    // 检查发布新闻按钮
    await expect(page.locator('text=发布新闻')).toBeVisible();
    
    // 检查搜索表单
    await expect(page.locator('input[placeholder*="新闻标题"]')).toBeVisible();
    
    // 检查表格
    await expect(page.locator('.ant-table')).toBeVisible();
    
    // 检查表格列标题
    await expect(page.locator('text=新闻标题')).toBeVisible();
    await expect(page.locator('text=类别')).toBeVisible();
    await expect(page.locator('text=状态')).toBeVisible();
    await expect(page.locator('text=发布日期')).toBeVisible();
    await expect(page.locator('text=操作')).toBeVisible();
  });

  test('发布新闻模态框UI测试', async ({ page }) => {
    // 点击发布新闻按钮
    await page.locator('text=发布新闻').click();
    
    // 检查模态框是否打开
    await expect(page.locator('.ant-modal')).toBeVisible();
    await expect(page.locator('text=发布新闻')).toBeVisible();
    
    // 检查表单字段
    await expect(page.locator('input[placeholder*="新闻标题"]')).toBeVisible();
    await expect(page.locator('textarea[placeholder*="新闻摘要"]')).toBeVisible();
    await expect(page.locator('textarea[placeholder*="新闻内容"]')).toBeVisible();
    
    // 检查新闻类别选择器
    const categorySelect = page.locator('.ant-select').filter({ hasText: /新闻类别/ });
    await expect(categorySelect).toBeVisible();
    
    // 检查日期选择器
    await expect(page.locator('.ant-picker')).toBeVisible();
    
    // 检查状态开关
    await expect(page.locator('.ant-switch')).toBeVisible();
    
    // 关闭模态框
    await page.locator('button:has-text("取消")').click();
    await expect(page.locator('.ant-modal')).not.toBeVisible();
  });

  test('新闻表格内容显示测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.ant-table-tbody tr', { timeout: 10000 });
    
    // 检查表格行是否存在
    const tableRows = page.locator('.ant-table-tbody tr');
    const rowCount = await tableRows.count();
    
    if (rowCount > 0) {
      // 检查第一行的内容
      const firstRow = tableRows.first();
      await expect(firstRow).toBeVisible();
      
      // 检查新闻标题列
      const titleCell = firstRow.locator('td').first();
      await expect(titleCell).toBeVisible();
      
      // 检查类别标签
      const categoryTag = firstRow.locator('.ant-tag');
      if (await categoryTag.count() > 0) {
        await expect(categoryTag.first()).toBeVisible();
      }
      
      // 检查状态标签
      const statusTag = firstRow.locator('.ant-tag').nth(1);
      if (await statusTag.isVisible()) {
        await expect(statusTag).toBeVisible();
      }
    }
  });

  test('搜索和筛选功能UI测试', async ({ page }) => {
    // 测试标题搜索
    const titleSearch = page.locator('input[placeholder*="新闻标题"]');
    await expect(titleSearch).toBeVisible();
    await titleSearch.fill('测试新闻');
    
    // 测试类别筛选
    const categoryFilter = page.locator('.ant-select').filter({ hasText: /类别/ });
    if (await categoryFilter.isVisible()) {
      await categoryFilter.click();
      await page.locator('text=等离子清洗机百科').click();
    }
    
    // 测试状态筛选
    const statusFilter = page.locator('.ant-select').filter({ hasText: /状态/ });
    if (await statusFilter.isVisible()) {
      await statusFilter.click();
      await page.locator('text=已发布').click();
    }
    
    // 点击查询按钮
    const searchButton = page.locator('button:has-text("查询")');
    if (await searchButton.isVisible()) {
      await searchButton.click();
      await page.waitForTimeout(1000);
    }
    
    // 点击重置按钮
    const resetButton = page.locator('button:has-text("重置")');
    if (await resetButton.isVisible()) {
      await resetButton.click();
      await expect(titleSearch).toHaveValue('');
    }
  });

  test('新闻编辑功能UI测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.ant-table-tbody tr', { timeout: 10000 });
    
    const editButtons = page.locator('button:has-text("编辑")');
    if (await editButtons.count() > 0) {
      // 点击第一个编辑按钮
      await editButtons.first().click();
      
      // 检查编辑模态框
      await expect(page.locator('.ant-modal')).toBeVisible();
      await expect(page.locator('text=编辑新闻')).toBeVisible();
      
      // 检查表单是否预填充了数据
      const titleInput = page.locator('input[placeholder*="新闻标题"]');
      const titleValue = await titleInput.inputValue();
      expect(titleValue.length).toBeGreaterThan(0);
      
      // 关闭模态框
      await page.locator('button:has-text("取消")').click();
    }
  });

  test('新闻删除功能UI测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.ant-table-tbody tr', { timeout: 10000 });
    
    const deleteButtons = page.locator('button:has-text("删除")');
    if (await deleteButtons.count() > 0) {
      // 点击第一个删除按钮
      await deleteButtons.first().click();
      
      // 检查确认对话框
      await expect(page.locator('.ant-popconfirm')).toBeVisible();
      await expect(page.locator('text=确定要删除这条新闻吗？')).toBeVisible();
      
      // 检查确认和取消按钮
      await expect(page.locator('button:has-text("确定")')).toBeVisible();
      await expect(page.locator('button:has-text("取消")')).toBeVisible();
      
      // 取消删除
      await page.locator('button:has-text("取消")').click();
      await expect(page.locator('.ant-popconfirm')).not.toBeVisible();
    }
  });

  test('表单验证UI测试', async ({ page }) => {
    // 打开发布新闻模态框
    await page.locator('text=发布新闻').click();
    await expect(page.locator('.ant-modal')).toBeVisible();
    
    // 尝试提交空表单
    await page.locator('button:has-text("确定")').click();
    
    // 检查验证错误信息
    await expect(page.locator('text=请输入新闻标题')).toBeVisible();
    await expect(page.locator('text=请选择新闻类别')).toBeVisible();
    await expect(page.locator('text=请选择发布日期')).toBeVisible();
    
    // 填写标题
    await page.locator('input[placeholder*="新闻标题"]').fill('测试新闻标题');
    
    // 检查标题验证错误是否消失
    await page.waitForTimeout(500);
    await expect(page.locator('text=请输入新闻标题')).not.toBeVisible();
    
    // 关闭模态框
    await page.locator('button:has-text("取消")').click();
  });

  test('日期选择器UI测试', async ({ page }) => {
    // 打开发布新闻模态框
    await page.locator('text=发布新闻').click();
    await expect(page.locator('.ant-modal')).toBeVisible();
    
    // 点击日期选择器
    const datePicker = page.locator('.ant-picker');
    await expect(datePicker).toBeVisible();
    await datePicker.click();
    
    // 检查日期面板是否打开
    await expect(page.locator('.ant-picker-dropdown')).toBeVisible();
    
    // 选择今天的日期
    const todayButton = page.locator('.ant-picker-today-btn');
    if (await todayButton.isVisible()) {
      await todayButton.click();
    } else {
      // 点击当前日期
      await page.locator('.ant-picker-cell-today').click();
    }
    
    // 检查日期是否被选中
    const selectedDate = await datePicker.inputValue();
    expect(selectedDate.length).toBeGreaterThan(0);
    
    // 关闭模态框
    await page.locator('button:has-text("取消")').click();
  });

  test('状态开关UI测试', async ({ page }) => {
    // 打开发布新闻模态框
    await page.locator('text=发布新闻').click();
    await expect(page.locator('.ant-modal')).toBeVisible();
    
    // 检查状态开关
    const statusSwitch = page.locator('.ant-switch');
    await expect(statusSwitch).toBeVisible();
    
    // 检查开关的初始状态（应该是开启的）
    const isChecked = await statusSwitch.isChecked();
    expect(isChecked).toBe(true);
    
    // 点击开关
    await statusSwitch.click();
    
    // 检查状态是否改变
    const newState = await statusSwitch.isChecked();
    expect(newState).toBe(false);
    
    // 再次点击恢复
    await statusSwitch.click();
    const finalState = await statusSwitch.isChecked();
    expect(finalState).toBe(true);
    
    // 关闭模态框
    await page.locator('button:has-text("取消")').click();
  });

  test('响应式设计测试', async ({ page }) => {
    // 桌面端测试
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('.ant-table')).toBeVisible();
    
    // 平板端测试
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500);
    await expect(page.locator('.ant-table')).toBeVisible();
    
    // 手机端测试
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    await expect(page.locator('.ant-table')).toBeVisible();
    
    // 检查手机端表格是否可以水平滚动
    const tableContainer = page.locator('.ant-table-container');
    const containerStyles = await tableContainer.evaluate(el => getComputedStyle(el));
    expect(containerStyles.overflowX).toBe('auto');
    
    // 测试手机端模态框
    await page.locator('text=发布新闻').click();
    const modal = page.locator('.ant-modal');
    await expect(modal).toBeVisible();
    
    // 检查模态框在手机端的宽度
    const modalBox = await modal.boundingBox();
    expect(modalBox?.width).toBeLessThan(400); // 应该适应手机屏幕
    
    await page.locator('button:has-text("取消")').click();
  });

  test('按钮样式和交互测试', async ({ page }) => {
    // 测试发布新闻按钮
    const publishButton = page.locator('text=发布新闻');
    await expect(publishButton).toBeVisible();
    
    // 检查按钮样式
    const buttonStyles = await publishButton.evaluate(el => getComputedStyle(el));
    expect(buttonStyles.background).toContain('gradient');
    expect(buttonStyles.borderRadius).not.toBe('0px');
    
    // 测试悬停效果
    await publishButton.hover();
    await page.waitForTimeout(300);
    
    const hoverStyles = await publishButton.evaluate(el => getComputedStyle(el));
    expect(hoverStyles.transform).toContain('translateY');
    
    // 检查按钮尺寸（触摸友好）
    const buttonBox = await publishButton.boundingBox();
    expect(buttonBox?.height).toBeGreaterThan(32);
    expect(buttonBox?.width).toBeGreaterThan(80);
  });
});
