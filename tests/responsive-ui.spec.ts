import { test, expect } from '@playwright/test';

test.describe('响应式设计UI测试', () => {
  test.beforeEach(async ({ page }) => {
    // 登录到管理后台
    await page.goto('/admin');
    await page.locator('input[type="password"]').fill('admin123');
    await page.locator('button[type="submit"]').click();
    await page.waitForURL('/admin');
  });

  const viewports = [
    { name: '4K显示器', width: 3840, height: 2160 },
    { name: '桌面端', width: 1920, height: 1080 },
    { name: '笔记本', width: 1366, height: 768 },
    { name: '平板横屏', width: 1024, height: 768 },
    { name: '平板竖屏', width: 768, height: 1024 },
    { name: '手机横屏', width: 667, height: 375 },
    { name: '手机竖屏', width: 375, height: 667 },
    { name: '小屏手机', width: 320, height: 568 },
  ];

  for (const viewport of viewports) {
    test(`${viewport.name} (${viewport.width}x${viewport.height}) 响应式测试`, async ({ page }) => {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(500);

      // 测试仪表盘布局
      await expect(page.locator('text=仪表盘')).toBeVisible();
      
      // 检查统计卡片布局
      const statisticCards = page.locator('.ant-statistic-card');
      const cardCount = await statisticCards.count();
      
      if (cardCount > 0) {
        // 检查卡片是否都可见
        for (let i = 0; i < Math.min(cardCount, 4); i++) {
          await expect(statisticCards.nth(i)).toBeVisible();
        }
        
        // 检查卡片布局
        if (viewport.width >= 1200) {
          // 大屏幕：卡片应该水平排列
          const firstCard = await statisticCards.first().boundingBox();
          const secondCard = await statisticCards.nth(1).boundingBox();
          if (firstCard && secondCard) {
            expect(Math.abs(firstCard.y - secondCard.y)).toBeLessThan(50);
          }
        } else if (viewport.width < 768) {
          // 小屏幕：卡片应该垂直堆叠
          const firstCard = await statisticCards.first().boundingBox();
          const secondCard = await statisticCards.nth(1).boundingBox();
          if (firstCard && secondCard) {
            expect(secondCard.y).toBeGreaterThan(firstCard.y + 50);
          }
        }
      }

      // 测试导航菜单
      if (viewport.width < 768) {
        // 小屏幕：菜单应该收起或隐藏
        const sider = page.locator('.ant-pro-layout-sider');
        if (await sider.isVisible()) {
          const siderWidth = await sider.evaluate(el => el.offsetWidth);
          expect(siderWidth).toBeLessThan(100);
        }
      } else {
        // 大屏幕：菜单应该展开
        const sider = page.locator('.ant-pro-layout-sider');
        if (await sider.isVisible()) {
          const siderWidth = await sider.evaluate(el => el.offsetWidth);
          expect(siderWidth).toBeGreaterThan(200);
        }
      }

      // 测试产品管理页面
      await page.locator('text=产品管理').click();
      await page.waitForURL(/.*products/);
      await page.waitForTimeout(500);

      // 检查表格响应式
      const table = page.locator('.ant-table');
      await expect(table).toBeVisible();

      if (viewport.width < 768) {
        // 小屏幕：表格应该可以水平滚动
        const tableContainer = page.locator('.ant-table-container');
        const containerStyles = await tableContainer.evaluate(el => getComputedStyle(el));
        expect(containerStyles.overflowX).toBe('auto');
      }

      // 测试添加产品模态框
      await page.locator('text=添加产品').click();
      const modal = page.locator('.ant-modal');
      await expect(modal).toBeVisible();

      const modalBox = await modal.boundingBox();
      if (viewport.width < 600) {
        // 小屏幕：模态框应该适应屏幕宽度
        expect(modalBox?.width).toBeLessThan(viewport.width - 40);
      }

      await page.locator('button:has-text("取消")').click();

      // 测试新闻管理页面
      await page.locator('text=新闻管理').click();
      await page.waitForURL(/.*news/);
      await page.waitForTimeout(500);

      await expect(page.locator('.ant-table')).toBeVisible();

      // 测试发布新闻模态框
      await page.locator('text=发布新闻').click();
      const newsModal = page.locator('.ant-modal');
      await expect(newsModal).toBeVisible();

      const newsModalBox = await newsModal.boundingBox();
      if (viewport.width < 800) {
        // 小屏幕：新闻模态框应该适应屏幕
        expect(newsModalBox?.width).toBeLessThan(viewport.width - 40);
      }

      await page.locator('button:has-text("取消")').click();
    });
  }

  test('触摸友好性测试', async ({ page }) => {
    // 模拟移动设备
    await page.setViewportSize({ width: 375, height: 667 });
    
    // 检查按钮尺寸（至少44px高度，苹果推荐）
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 10); i++) {
      const button = buttons.nth(i);
      if (await button.isVisible()) {
        const buttonBox = await button.boundingBox();
        if (buttonBox) {
          expect(buttonBox.height).toBeGreaterThan(40);
          expect(buttonBox.width).toBeGreaterThan(40);
        }
      }
    }

    // 检查链接的点击区域
    const links = page.locator('a');
    const linkCount = await links.count();
    
    for (let i = 0; i < Math.min(linkCount, 5); i++) {
      const link = links.nth(i);
      if (await link.isVisible()) {
        const linkBox = await link.boundingBox();
        if (linkBox) {
          expect(linkBox.height).toBeGreaterThan(32);
        }
      }
    }
  });

  test('文字可读性测试', async ({ page }) => {
    const viewports = [
      { width: 1920, height: 1080 },
      { width: 768, height: 1024 },
      { width: 375, height: 667 },
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(500);

      // 检查主要文本的字体大小
      const titleElements = page.locator('h1, h2, h3, .ant-typography-title');
      const titleCount = await titleElements.count();

      for (let i = 0; i < Math.min(titleCount, 5); i++) {
        const title = titleElements.nth(i);
        if (await title.isVisible()) {
          const titleStyles = await title.evaluate(el => getComputedStyle(el));
          const fontSize = parseInt(titleStyles.fontSize);
          
          if (viewport.width < 768) {
            // 移动端：标题字体不应小于16px
            expect(fontSize).toBeGreaterThanOrEqual(16);
          } else {
            // 桌面端：标题字体不应小于18px
            expect(fontSize).toBeGreaterThanOrEqual(18);
          }
        }
      }

      // 检查正文文本
      const textElements = page.locator('p, span, div').filter({ hasText: /\w+/ });
      const textCount = await textElements.count();

      for (let i = 0; i < Math.min(textCount, 10); i++) {
        const text = textElements.nth(i);
        if (await text.isVisible()) {
          const textStyles = await text.evaluate(el => getComputedStyle(el));
          const fontSize = parseInt(textStyles.fontSize);
          
          // 正文字体不应小于14px
          if (fontSize > 0) {
            expect(fontSize).toBeGreaterThanOrEqual(14);
          }
        }
      }
    }
  });

  test('滚动行为测试', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    // 测试页面垂直滚动
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await page.waitForTimeout(500);
    
    // 检查页面是否可以滚动回顶部
    await page.evaluate(() => window.scrollTo(0, 0));
    await page.waitForTimeout(500);
    
    // 测试表格水平滚动
    await page.locator('text=产品管理').click();
    await page.waitForURL(/.*products/);
    
    const tableContainer = page.locator('.ant-table-container');
    if (await tableContainer.isVisible()) {
      // 滚动表格到右侧
      await tableContainer.evaluate(el => el.scrollLeft = el.scrollWidth);
      await page.waitForTimeout(500);
      
      // 滚动回左侧
      await tableContainer.evaluate(el => el.scrollLeft = 0);
      await page.waitForTimeout(500);
    }
  });

  test('方向变化测试', async ({ page }) => {
    // 测试竖屏到横屏的变化
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('text=仪表盘')).toBeVisible();
    
    // 旋转到横屏
    await page.setViewportSize({ width: 667, height: 375 });
    await page.waitForTimeout(1000);
    await expect(page.locator('text=仪表盘')).toBeVisible();
    
    // 检查布局是否适应新的方向
    const statisticCards = page.locator('.ant-statistic-card');
    if (await statisticCards.count() > 0) {
      await expect(statisticCards.first()).toBeVisible();
    }
    
    // 旋转回竖屏
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    await expect(page.locator('text=仪表盘')).toBeVisible();
  });
});
