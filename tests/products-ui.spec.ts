import { test, expect } from '@playwright/test';

test.describe('产品管理页面UI测试', () => {
  test.beforeEach(async ({ page }) => {
    // 先登录并导航到产品管理页面
    await page.goto('/admin');
    await page.locator('input[type="password"]').fill('admin123');
    await page.locator('button[type="submit"]').click();
    await page.waitForURL('/admin');
    await page.locator('text=产品管理').click();
    await page.waitForURL(/.*products/);
  });

  test('产品管理页面基本布局', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('text=产品管理')).toBeVisible();
    
    // 检查添加产品按钮
    await expect(page.locator('text=添加产品')).toBeVisible();
    
    // 检查搜索表单
    await expect(page.locator('input[placeholder*="产品名称"]')).toBeVisible();
    
    // 检查表格
    await expect(page.locator('.ant-table')).toBeVisible();
    
    // 检查表格列标题
    await expect(page.locator('text=产品名称')).toBeVisible();
    await expect(page.locator('text=类别')).toBeVisible();
    await expect(page.locator('text=状态')).toBeVisible();
    await expect(page.locator('text=操作')).toBeVisible();
  });

  test('搜索功能UI测试', async ({ page }) => {
    // 测试搜索输入框
    const searchInput = page.locator('input[placeholder*="产品名称"]');
    await expect(searchInput).toBeVisible();
    
    // 输入搜索内容
    await searchInput.fill('测试产品');
    await page.keyboard.press('Enter');
    
    // 等待搜索结果
    await page.waitForTimeout(1000);
    
    // 检查搜索按钮
    const searchButton = page.locator('button:has-text("查询")');
    if (await searchButton.isVisible()) {
      await searchButton.click();
    }
    
    // 检查重置按钮
    const resetButton = page.locator('button:has-text("重置")');
    if (await resetButton.isVisible()) {
      await resetButton.click();
      await expect(searchInput).toHaveValue('');
    }
  });

  test('添加产品模态框UI测试', async ({ page }) => {
    // 点击添加产品按钮
    await page.locator('text=添加产品').click();
    
    // 检查模态框是否打开
    await expect(page.locator('.ant-modal')).toBeVisible();
    await expect(page.locator('text=添加产品')).toBeVisible();
    
    // 检查表单字段
    await expect(page.locator('input[placeholder*="产品名称"]')).toBeVisible();
    await expect(page.locator('input[placeholder*="产品型号"]')).toBeVisible();
    await expect(page.locator('.ant-select')).toBeVisible(); // 产品类别选择器
    await expect(page.locator('textarea[placeholder*="产品描述"]')).toBeVisible();
    
    // 检查表单按钮
    await expect(page.locator('button:has-text("取消")')).toBeVisible();
    await expect(page.locator('button:has-text("确定")')).toBeVisible();
    
    // 关闭模态框
    await page.locator('button:has-text("取消")').click();
    await expect(page.locator('.ant-modal')).not.toBeVisible();
  });

  test('表格操作按钮UI测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.ant-table-tbody tr');
    
    // 检查操作列是否存在
    const actionColumn = page.locator('th:has-text("操作")');
    await expect(actionColumn).toBeVisible();
    
    // 检查操作按钮
    const editButtons = page.locator('button:has-text("编辑")');
    const deleteButtons = page.locator('button:has-text("删除")');
    
    if (await editButtons.count() > 0) {
      await expect(editButtons.first()).toBeVisible();
      
      // 测试编辑按钮悬停效果
      await editButtons.first().hover();
      await page.waitForTimeout(300);
      
      const buttonStyles = await editButtons.first().evaluate(el => getComputedStyle(el));
      expect(buttonStyles.transform).toContain('translateY');
    }
    
    if (await deleteButtons.count() > 0) {
      await expect(deleteButtons.first()).toBeVisible();
      
      // 测试删除按钮点击（但不确认）
      await deleteButtons.first().click();
      await expect(page.locator('.ant-popconfirm')).toBeVisible();
      
      // 取消删除
      await page.locator('button:has-text("取消")').click();
    }
  });

  test('表格响应式设计测试', async ({ page }) => {
    // 桌面端测试
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('.ant-table')).toBeVisible();
    
    // 检查表格是否有水平滚动
    const table = page.locator('.ant-table');
    const tableBox = await table.boundingBox();
    expect(tableBox?.width).toBeGreaterThan(800);
    
    // 平板端测试
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500);
    await expect(table).toBeVisible();
    
    // 手机端测试
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    await expect(table).toBeVisible();
    
    // 检查手机端是否有水平滚动
    const tableContainer = page.locator('.ant-table-container');
    const containerStyles = await tableContainer.evaluate(el => getComputedStyle(el));
    expect(containerStyles.overflowX).toBe('auto');
  });

  test('分页组件UI测试', async ({ page }) => {
    // 检查分页组件
    const pagination = page.locator('.ant-pagination');
    if (await pagination.isVisible()) {
      // 检查分页信息
      await expect(page.locator('text=/第.*条，共.*条记录/')).toBeVisible();
      
      // 检查页码按钮
      const pageButtons = page.locator('.ant-pagination-item');
      if (await pageButtons.count() > 0) {
        await expect(pageButtons.first()).toBeVisible();
      }
      
      // 检查每页显示数量选择器
      const pageSizeSelector = page.locator('.ant-pagination-options-size-changer');
      if (await pageSizeSelector.isVisible()) {
        await expect(pageSizeSelector).toBeVisible();
      }
    }
  });

  test('表单验证UI测试', async ({ page }) => {
    // 打开添加产品模态框
    await page.locator('text=添加产品').click();
    await expect(page.locator('.ant-modal')).toBeVisible();
    
    // 尝试提交空表单
    await page.locator('button:has-text("确定")').click();
    
    // 检查验证错误信息
    await expect(page.locator('text=请输入产品名称')).toBeVisible();
    await expect(page.locator('text=请选择产品类别')).toBeVisible();
    
    // 填写部分信息
    await page.locator('input[placeholder*="产品名称"]').fill('测试产品');
    
    // 检查错误信息是否消失
    await page.waitForTimeout(500);
    await expect(page.locator('text=请输入产品名称')).not.toBeVisible();
    
    // 关闭模态框
    await page.locator('button:has-text("取消")').click();
  });

  test('按钮样式和交互测试', async ({ page }) => {
    // 测试添加产品按钮样式
    const addButton = page.locator('text=添加产品');
    await expect(addButton).toBeVisible();
    
    // 检查按钮样式
    const buttonStyles = await addButton.evaluate(el => getComputedStyle(el));
    expect(buttonStyles.background).toContain('gradient');
    expect(buttonStyles.borderRadius).not.toBe('0px');
    
    // 测试悬停效果
    await addButton.hover();
    await page.waitForTimeout(300);
    
    const hoverStyles = await addButton.evaluate(el => getComputedStyle(el));
    expect(hoverStyles.transform).toContain('translateY');
    
    // 测试按钮点击区域
    const buttonBox = await addButton.boundingBox();
    expect(buttonBox?.height).toBeGreaterThan(32); // 足够的点击区域
    expect(buttonBox?.width).toBeGreaterThan(80);
  });

  test('颜色对比度测试', async ({ page }) => {
    // 检查页面标题的颜色对比度
    const title = page.locator('text=产品管理').first();
    const titleStyles = await title.evaluate(el => getComputedStyle(el));
    
    // 检查文本颜色
    expect(titleStyles.color).not.toBe('rgba(0, 0, 0, 0)');
    expect(titleStyles.color).not.toBe('transparent');
    
    // 检查表格文本的可读性
    const tableCell = page.locator('.ant-table-tbody td').first();
    if (await tableCell.isVisible()) {
      const cellStyles = await tableCell.evaluate(el => getComputedStyle(el));
      expect(parseInt(cellStyles.fontSize)).toBeGreaterThan(12);
    }
  });

  test('加载状态测试', async ({ page }) => {
    // 刷新页面观察加载状态
    await page.reload();
    
    // 检查是否有加载指示器
    const loadingIndicator = page.locator('.ant-spin');
    if (await loadingIndicator.isVisible()) {
      await expect(loadingIndicator).toBeVisible();
      
      // 等待加载完成
      await loadingIndicator.waitFor({ state: 'hidden', timeout: 5000 });
    }
    
    // 确保表格最终加载完成
    await expect(page.locator('.ant-table')).toBeVisible();
  });

  test('键盘导航测试', async ({ page }) => {
    // 测试Tab键导航
    await page.keyboard.press('Tab');
    
    // 检查焦点是否在搜索输入框
    const searchInput = page.locator('input[placeholder*="产品名称"]');
    if (await searchInput.isVisible()) {
      await expect(searchInput).toBeFocused();
    }
    
    // 继续Tab导航
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // 测试Enter键操作
    await page.keyboard.press('Enter');
  });
});
