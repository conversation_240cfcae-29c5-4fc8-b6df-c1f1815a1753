import { test, expect } from '@playwright/test';

test.describe('登录页面UI测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/admin');
  });

  test('登录页面布局和视觉元素', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/等离子清洗专家/);
    
    // 检查登录卡片是否存在
    const loginCard = page.locator('.ant-pro-card');
    await expect(loginCard).toBeVisible();
    
    // 检查Logo和标题
    await expect(page.locator('text=管理后台登录')).toBeVisible();
    await expect(page.locator('text=等离子清洗专家 - 管理系统')).toBeVisible();
    
    // 检查表单元素
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    await expect(page.locator('text=返回网站首页')).toBeVisible();
    
    // 检查演示说明
    await expect(page.locator('text=演示说明')).toBeVisible();
    await expect(page.locator('text=admin123')).toBeVisible();
  });

  test('登录表单交互测试', async ({ page }) => {
    const passwordInput = page.locator('input[type="password"]');
    const submitButton = page.locator('button[type="submit"]');
    
    // 测试空密码提交
    await submitButton.click();
    await expect(page.locator('text=请输入管理密码')).toBeVisible();
    
    // 测试错误密码
    await passwordInput.fill('wrongpassword');
    await submitButton.click();
    await expect(page.locator('.ant-alert-error')).toBeVisible();
    
    // 测试正确密码
    await passwordInput.fill('admin123');
    await submitButton.click();
    
    // 应该跳转到仪表盘
    await expect(page).toHaveURL('/admin');
    await expect(page.locator('text=仪表盘')).toBeVisible();
  });

  test('登录页面响应式设计', async ({ page }) => {
    // 测试桌面端
    await page.setViewportSize({ width: 1920, height: 1080 });
    const loginCard = page.locator('.ant-pro-card');
    await expect(loginCard).toBeVisible();
    
    // 测试平板端
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(loginCard).toBeVisible();
    
    // 测试手机端
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(loginCard).toBeVisible();
    
    // 检查手机端按钮是否足够大（触摸友好）
    const submitButton = page.locator('button[type="submit"]');
    const buttonBox = await submitButton.boundingBox();
    expect(buttonBox?.height).toBeGreaterThan(40); // 至少40px高度
  });

  test('登录页面可访问性测试', async ({ page }) => {
    // 检查表单标签
    await expect(page.locator('label:has-text("管理密码")')).toBeVisible();
    
    // 检查按钮文本
    await expect(page.locator('button:has-text("登录系统")')).toBeVisible();
    
    // 检查链接可访问性
    const homeLink = page.locator('text=返回网站首页');
    await expect(homeLink).toBeVisible();
    
    // 测试键盘导航
    await page.keyboard.press('Tab');
    await expect(page.locator('input[type="password"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('button[type="submit"]')).toBeFocused();
  });

  test('登录页面视觉效果测试', async ({ page }) => {
    // 检查背景渐变
    const body = page.locator('body');
    const bodyStyles = await body.evaluate(el => getComputedStyle(el));
    expect(bodyStyles.background).toContain('gradient');
    
    // 检查卡片阴影效果
    const loginCard = page.locator('.ant-pro-card');
    const cardStyles = await loginCard.evaluate(el => getComputedStyle(el));
    expect(cardStyles.boxShadow).not.toBe('none');
    
    // 检查按钮悬停效果
    const submitButton = page.locator('button[type="submit"]');
    await submitButton.hover();
    
    // 等待动画完成
    await page.waitForTimeout(500);
    
    // 检查悬停后的样式变化
    const buttonStyles = await submitButton.evaluate(el => getComputedStyle(el));
    expect(buttonStyles.transform).toContain('translateY');
  });

  test('登录页面加载性能测试', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/admin');
    await page.locator('.ant-pro-card').waitFor();
    
    const loadTime = Date.now() - startTime;
    
    // 页面应该在2秒内加载完成
    expect(loadTime).toBeLessThan(2000);
    
    // 检查关键元素是否都已加载
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('登录页面错误处理测试', async ({ page }) => {
    const passwordInput = page.locator('input[type="password"]');
    const submitButton = page.locator('button[type="submit"]');
    
    // 测试网络错误模拟（如果需要）
    await passwordInput.fill('admin123');
    
    // 模拟慢网络
    await page.route('**/api/**', route => {
      setTimeout(() => route.continue(), 1000);
    });
    
    await submitButton.click();
    
    // 检查是否有加载状态指示
    // 这里可以根据实际实现来检查加载状态
  });
});
