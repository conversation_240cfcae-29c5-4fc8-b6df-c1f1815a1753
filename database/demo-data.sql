-- 演示数据初始化脚本
-- 数据库: juli_web

-- 创建演示数据表（如果不存在）
CREATE TABLE IF NOT EXISTS demo_materials (
  id VARCHAR(50) PRIMARY KEY COMMENT '材料ID',
  name VARCHAR(100) NOT NULL COMMENT '材料名称',
  display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
  description TEXT COMMENT '材料描述',
  color VARCHAR(20) NOT NULL COMMENT '材料颜色',
  roughness DECIMAL(3,2) DEFAULT 0.1 COMMENT '粗糙度',
  metalness DECIMAL(3,2) DEFAULT 0 COMMENT '金属度',
  model_path VARCHAR(255) COMMENT '3D模型路径',
  surface_properties JSON COMMENT '表面性质数据',
  applications JSON COMMENT '应用领域',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='演示材料表';

CREATE TABLE IF NOT EXISTS demo_processes (
  id VARCHAR(50) PRIMARY KEY COMMENT '工艺ID',
  name VARCHAR(100) NOT NULL COMMENT '工艺名称',
  display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
  description TEXT COMMENT '工艺描述',
  color VARCHAR(20) NOT NULL COMMENT '工艺颜色',
  duration INT DEFAULT 30 COMMENT '处理时长(秒)',
  parameters JSON COMMENT '工艺参数',
  effects JSON COMMENT '工艺效果',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='演示工艺表';

CREATE TABLE IF NOT EXISTS demo_material_processes (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  material_id VARCHAR(50) NOT NULL COMMENT '材料ID',
  process_id VARCHAR(50) NOT NULL COMMENT '工艺ID',
  custom_parameters JSON COMMENT '自定义参数',
  custom_effects JSON COMMENT '自定义效果',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY unique_material_process (material_id, process_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='材料工艺关联表';

CREATE TABLE IF NOT EXISTS demo_configs (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
  config_value JSON NOT NULL COMMENT '配置值',
  description TEXT COMMENT '配置描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='演示配置表';

CREATE TABLE IF NOT EXISTS demo_products (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  name VARCHAR(200) NOT NULL COMMENT '产品名称',
  model VARCHAR(100) COMMENT '产品型号',
  slug VARCHAR(100) NOT NULL COMMENT 'URL别名',
  category VARCHAR(50) NOT NULL COMMENT '产品类别',
  description TEXT COMMENT '产品描述',
  href VARCHAR(255) COMMENT '链接地址',
  image VARCHAR(255) COMMENT '产品图片',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='演示产品表';

CREATE TABLE IF NOT EXISTS demo_news (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  title VARCHAR(500) NOT NULL COMMENT '新闻标题',
  excerpt TEXT COMMENT '新闻摘要',
  content TEXT COMMENT '新闻内容',
  category VARCHAR(50) NOT NULL COMMENT '新闻类别',
  href VARCHAR(255) COMMENT '链接地址',
  publish_date DATE NOT NULL COMMENT '发布日期',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='演示新闻表';

-- 清空现有数据
DELETE FROM demo_materials;
DELETE FROM demo_processes;
DELETE FROM demo_material_processes;
DELETE FROM demo_configs;
DELETE FROM demo_products;
DELETE FROM demo_news;

-- 插入材料数据
INSERT INTO demo_materials (id, name, display_name, description, color, roughness, metalness, model_path, surface_properties, applications) VALUES
('polymer', 'polymer', '聚合物', 'PP、PC等塑料材料', '#4CAF50', 0.1, 0, '/models/polymer_sample.glb', 
 '{"contactAngle":{"before":95,"after":25},"surfaceEnergy":{"before":32,"after":68},"roughnessChange":{"before":0.1,"after":0.3}}',
 '["汽车零件","电子外壳","包装材料","医疗器械"]'),

('metal', 'metal', '金属', '铝、不锈钢等金属', '#9E9E9E', 0.3, 1, '/models/metal_sample.glb',
 '{"contactAngle":{"before":85,"after":15},"surfaceEnergy":{"before":45,"after":72},"roughnessChange":{"before":0.3,"after":0.5}}',
 '["航空航天","汽车工业","电子设备","医疗植入物"]'),

('glass_ceramic', 'glass_ceramic', '玻璃陶瓷', '玻璃、陶瓷等无机材料', '#2196F3', 0.1, 0, '/models/glass_sample.glb',
 '{"contactAngle":{"before":75,"after":8},"surfaceEnergy":{"before":38,"after":65},"roughnessChange":{"before":0.1,"after":0.25}}',
 '["光学器件","电子基板","生物医学","装饰材料"]');

-- 插入工艺数据
INSERT INTO demo_processes (id, name, display_name, description, color, duration, parameters, effects) VALUES
('activation', 'activation', '表面活化', '提高表面能，改善润湿性和粘接性能', '#9C27B0', 30,
 '{"power":100,"gasFlow":50,"speed":10,"gas":"O₂/Ar","pressure":0.5,"temperature":25}',
 '["提高表面能","改善润湿性","增强粘接力"]'),

('etching', 'etching', '等离子刻蚀', '去除氧化层和污染物，增加表面粗糙度', '#FF5722', 45,
 '{"power":150,"gasFlow":80,"speed":8,"gas":"Ar/CF₄","pressure":0.8,"temperature":35}',
 '["去除污染物","增加粗糙度","提高附着力"]'),

('coating', 'coating', '等离子涂层', '在表面沉积功能性薄膜层', '#00BCD4', 60,
 '{"power":120,"gasFlow":60,"speed":6,"gas":"SiH₄/N₂","pressure":1.2,"temperature":45}',
 '["沉积薄膜","改善性能","增加功能性"]');

-- 插入演示产品数据
INSERT INTO demo_products (name, model, slug, category, description, href, image, sort_order) VALUES
('小型等离子清洗机', 'PM-20LN', 'small-vacuum', 'vacuum', '适用于小批量样品处理的真空等离子清洗设备', '/products/small-vacuum', '/images/products/pm-20ln.jpg', 1),
('大型等离子清洗机', 'PM-2300LNR60LN', 'large-vacuum', 'vacuum', '适用于大批量生产的真空等离子清洗设备', '/products/large-vacuum', '/images/products/pm-2300.jpg', 2),
('医疗导管等离子清洗机', 'PM-210LN', 'medical-catheter', 'medical', '专为医疗导管等医疗器械设计的等离子清洗设备', '/products/large-vacuum', '/images/products/pm-210ln.jpg', 3),
('等离子干刻机', 'JY-36LN', 'dry-etching', 'etching', '专业的等离子干刻设备，适用于微电子加工', '/products/large-vacuum', '/images/products/jy-36ln.jpg', 4),
('小型等离子清洗机', 'PM-3LN', 'mini-vacuum', 'vacuum', '超小型等离子清洗设备，适合桌面使用', '/products/small-vacuum', '/images/products/pm-3ln.jpg', 5),
('大气等离子清洗设备', 'AP-PM1000', 'atmospheric', 'atmospheric', '常压下工作的等离子清洗设备，无需真空系统', '/products/atmospheric', '/images/products/ap-pm1000.jpg', 6),
('薄膜等离子清洗机', 'AP-800-AJR', 'film-atmospheric', 'atmospheric', '专为薄膜材料设计的大气等离子清洗设备', '/products/atmospheric', '/images/products/ap-800.jpg', 7),
('真空等离子清洗机', 'PM/R-80L', 'vacuum-80l', 'vacuum', '大容量真空等离子清洗设备，适用于批量处理', '/products/large-vacuum', '/images/products/pm-r-80l.jpg', 8);

-- 插入演示新闻数据
INSERT INTO demo_news (title, excerpt, category, href, publish_date, sort_order) VALUES
('微流控PDMS芯片键合等离子清洗机应用-微流控pdms芯片键', '微流控PDMS芯片键合等离子清洗机是一种用于清洗微流控PDMS芯片的设备，该设备在微流控技术和等离子清洗技术的...', 'applications', '/news/applications', '2025-07-24', 1),
('等离子外加工清洗机、等离子外加工清洗机：高效清洗新选择', '随着现代工业的不断发展，清洗工艺也得到了越来越多的关注。传统的清洗方法可能存在对环境的污染、清洗效率低下等问题...', 'applications', '/news/applications', '2025-07-24', 2),
('改善聚丙烯腈PAN润湿性和粘接性 改善材料表面的性能(改善聚', '随着科学技术的不断发展，材料科学领域也在不断进步。在材料研究中，表面性能的改善一直是研究的重点之一。本文将围绕...', 'applications', '/news/applications', '2025-07-24', 3),
('等离子除胶处理机使用方法—等离子除胶处理机使用指南', '等离子除胶处理机使用指南你是否曾经遇到过这样的问题：胶水在工业生产过程中难以去除，导致产品质量下降，甚至无法正...', 'applications', '/news/applications', '2025-07-24', 4);
