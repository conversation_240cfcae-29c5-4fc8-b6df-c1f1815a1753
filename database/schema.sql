-- Database Schema for Plasma Website
-- Database: juli_web

CREATE DATABASE IF NOT EXISTS juli_web CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE juli_web;

-- Categories table for both articles and products
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    type ENUM('article', 'product') NOT NULL,
    description TEXT,
    parent_id INT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Articles table for news and content
CREATE TABLE articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    excerpt TEXT,
    content LONGTEXT,
    featured_image VARCHAR(255),
    category_id INT,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    views INT DEFAULT 0,
    sort_order INT DEFAULT 0,
    seo_title VARCHAR(255),
    seo_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_category (category_id),
    INDEX idx_published (published_at)
);

-- Products table
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    model VARCHAR(100),
    description TEXT,
    specifications LONGTEXT,
    features LONGTEXT,
    applications TEXT,
    featured_image VARCHAR(255),
    gallery JSON,
    category_id INT,
    price DECIMAL(10,2),
    status ENUM('active', 'inactive', 'discontinued') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    seo_title VARCHAR(255),
    seo_description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_category (category_id)
);

-- Company information table
CREATE TABLE company_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section VARCHAR(50) NOT NULL UNIQUE,
    title VARCHAR(255),
    content LONGTEXT,
    data JSON,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    company VARCHAR(255),
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_created (created_at)
);

-- Settings table for site configuration
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(100) NOT NULL UNIQUE,
    value LONGTEXT,
    type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert initial categories
INSERT INTO categories (name, slug, type, description, sort_order) VALUES
('等离子清洗机百科', 'dlzqxjbk', 'article', '等离子清洗机相关知识和技术文章', 1),
('等离子清洗机应用', 'dlzqxjyy', 'article', '等离子清洗机应用案例和解决方案', 2),
('小型真空等离子清洗机', 'xxzkdlz', 'product', '小型真空等离子清洗设备', 1),
('大型真空等离子清洗机', 'dxzkdlz', 'product', '大型真空等离子清洗设备', 2),
('大气等离子清洗机', 'dqdlz', 'product', '大气等离子清洗设备', 3);

-- Insert company information
INSERT INTO company_info (section, title, content) VALUES
('about', '关于我们', '昆山普乐斯电子科技有限公司是等离子清洗机品牌厂家，集等离子表面处理技术研发、制造、销售和推广于一体的高新技术企业，已通过ISO9001质量体系、欧盟CE和企业知识产权管理体系等认证。'),
('contact', '联系我们', '{"name": "昆山普乐斯电子科技有限公司", "contact_person": "周女士", "phone": "13382151102", "email": "<EMAIL>", "address": "江苏省昆山市巴城镇东盛路298号3号厂房", "hotline": "************"}'),
('values', '企业价值观', '诚信立足、创新致远、开放包容、合作共生'),
('services', '服务优势', '工艺是等离子体技术应用的基础，而不断创新是企业发展和进步的源动力');

-- Insert initial settings
INSERT INTO settings (key_name, value, type, description) VALUES
('site_title', '等离子清洗机-大气等离子清洗机-真空等离子清洗设备-昆山普乐斯电子科技有限公司', 'string', '网站标题'),
('site_description', '昆山普乐斯电子科技有限公司专业生产等离子清洗机设备', 'string', '网站描述'),
('company_name', '昆山普乐斯电子科技有限公司', 'string', '公司名称'),
('copyright', 'Copyright © 2012-2018 昆山普乐斯电子科技有限公司 版权所有', 'string', '版权信息'),
('icp', '苏ICP备11060938号-3', 'string', 'ICP备案号');
