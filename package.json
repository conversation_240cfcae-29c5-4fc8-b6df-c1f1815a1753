{"name": "juli_get_info", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "start:safe": "npm run build && npm start", "clean": "rimraf .next && npm cache clean --force", "clean:build": "npm run clean && npm run build", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/pro-card": "^2.10.0", "@ant-design/pro-components": "^2.8.10", "@ant-design/pro-form": "^2.32.0", "@ant-design/pro-layout": "^7.22.7", "@ant-design/pro-table": "^3.21.0", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "@types/three": "^0.178.1", "antd": "^5.22.6", "dayjs": "^1.11.13", "geist": "^1.4.2", "mysql2": "^3.14.2", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "three": "^0.178.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "rimraf": "^6.0.1", "tailwindcss": "^4", "typescript": "^5"}}