# 等离子清洗专家 - 管理后台 (Ant Design版本)

## 功能概述

这是一个基于Ant Design组件库的现代化等离子清洗设备网站管理后台，包含产品管理和新闻管理功能。

## 主要功能

### 1. 产品管理 (`/admin/products`)
- ✅ 产品列表展示（分页、搜索、筛选）
- ✅ 添加新产品
- ✅ 编辑产品信息
- ✅ 删除产品
- ✅ 产品状态管理（启用/禁用）
- ✅ 产品分类筛选
- ✅ 排序功能

### 2. 新闻管理 (`/admin/news`)
- ✅ 新闻列表展示（分页、搜索、筛选）
- ✅ 发布新闻
- ✅ 编辑新闻内容
- ✅ 删除新闻
- ✅ 新闻状态管理（已发布/草稿）
- ✅ 新闻分类筛选
- ✅ 发布日期管理
- ✅ **自动生成新闻地址** - 根据标题自动生成URL

### 3. 身份验证
- ✅ 简单密码登录（演示版本）
- ✅ 登录状态保持
- ✅ 安全退出

### 4. 新增功能特性
- ✅ **现代化UI设计** - 基于Ant Design组件库
- ✅ **响应式布局** - 支持桌面和移动设备
- ✅ **智能表格** - 支持排序、筛选、分页
- ✅ **表单验证** - 实时表单验证和错误提示
- ✅ **消息提醒** - 操作成功/失败的即时反馈
- ✅ **确认对话框** - 删除操作的安全确认
- ✅ **自动URL生成** - 新闻标题自动生成友好URL
- ✅ **日期选择器** - 直观的日期选择组件
- ✅ **状态切换** - 开关组件控制启用/禁用状态

## 技术栈

- **前端**: Next.js 15, React, TypeScript, Ant Design 5.x
- **UI组件**: Ant Design (antd), Ant Design Icons
- **后端**: Next.js API Routes
- **数据库**: MySQL 8.0
- **ORM**: 原生 MySQL2 连接池
- **日期处理**: Day.js

## 数据库结构

### 产品表 (demo_products)
```sql
- id: 主键
- name: 产品名称
- model: 产品型号
- slug: URL别名
- category: 产品类别 (vacuum, atmospheric, medical, etching)
- description: 产品描述
- href: 链接地址
- image: 产品图片
- sort_order: 排序
- is_active: 是否启用
- created_at: 创建时间
- updated_at: 更新时间
```

### 新闻表 (demo_news)
```sql
- id: 主键
- title: 新闻标题
- excerpt: 新闻摘要
- content: 新闻内容
- category: 新闻类别 (encyclopedia, applications, technology, industry)
- href: 链接地址
- publish_date: 发布日期
- sort_order: 排序
- is_active: 是否发布
- created_at: 创建时间
- updated_at: 更新时间
```

## 使用说明

### 1. 访问管理后台
访问 `http://localhost:3000/admin`

### 2. 登录
- 默认密码: `admin123`
- 登录后可以访问所有管理功能

### 3. 产品管理
- 点击"产品管理"进入产品列表
- 使用搜索框搜索产品名称、型号
- 使用筛选器按类别和状态筛选
- 点击"添加产品"创建新产品
- 点击"编辑"修改产品信息
- 点击"删除"删除产品

### 4. 新闻管理
- 点击"新闻管理"进入新闻列表
- 使用搜索框搜索新闻标题、内容
- 使用筛选器按类别和状态筛选
- 点击"发布新闻"创建新新闻
- 点击"编辑"修改新闻内容
- 点击"删除"删除新闻

### 5. 新功能使用说明

#### 自动URL生成
- 在创建新闻时，输入标题后会自动生成URL地址
- 生成的URL格式为：`/news/标题-转换-为-url-格式`
- 可以手动修改生成的URL地址
- 编辑现有新闻时不会自动覆盖已有URL

#### 高级搜索和筛选
- 支持实时搜索，无需点击搜索按钮
- 多条件组合筛选（类别 + 状态）
- 一键重置所有筛选条件

#### 表格功能
- 点击列标题进行排序
- 支持每页显示数量调整
- 快速跳转到指定页面
- 显示详细的分页信息

## API 接口

### 产品 API
- `GET /api/admin/products` - 获取产品列表
- `POST /api/admin/products` - 创建产品
- `PUT /api/admin/products` - 更新产品
- `DELETE /api/admin/products?id={id}` - 删除产品

### 新闻 API
- `GET /api/admin/news` - 获取新闻列表
- `POST /api/admin/news` - 创建新闻
- `PUT /api/admin/news` - 更新新闻
- `DELETE /api/admin/news?id={id}` - 删除新闻

## 安全说明

⚠️ **重要**: 这是演示版本，使用了简单的密码验证。在生产环境中，请：

1. 使用更安全的身份验证方式（JWT、OAuth等）
2. 添加CSRF保护
3. 实施更严格的权限控制
4. 使用HTTPS
5. 添加输入验证和SQL注入防护
6. 实施日志记录和监控

## 开发说明

### 数据库初始化
运行以下命令初始化数据库：
```bash
mysql -u root -p < scripts/init-database.sql
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
npm start
```

## 文件结构

```
src/
├── app/
│   ├── admin/
│   │   ├── layout.tsx          # 管理后台布局和认证
│   │   ├── page.tsx            # 管理后台首页
│   │   ├── products/
│   │   │   └── page.tsx        # 产品管理页面
│   │   └── news/
│   │       └── page.tsx        # 新闻管理页面
│   └── api/
│       └── admin/
│           ├── products/
│           │   └── route.ts    # 产品API
│           └── news/
│               └── route.ts    # 新闻API
├── components/
│   └── admin/
│       └── AdminLayout.tsx     # 管理后台组件
└── lib/
    └── db.ts                   # 数据库连接
```

## 更新日志

### v2.0.0 (2025-07-30) - Ant Design版本
- ✅ **重大更新**: 使用Ant Design重构整个管理后台
- ✅ **新增功能**: 新闻地址自动生成
- ✅ **UI升级**: 现代化的Ant Design组件界面
- ✅ **用户体验**: 更好的表单验证和消息提醒
- ✅ **表格增强**: 高级表格功能（排序、筛选、分页）
- ✅ **响应式设计**: 更好的移动端适配
- ✅ **交互优化**: 确认对话框、加载状态、错误处理

### v1.0.0 (2025-07-30) - 初始版本
- ✅ 完成产品管理功能
- ✅ 完成新闻管理功能
- ✅ 实现基础身份验证
- ✅ 添加分页、搜索、筛选功能
- ✅ 创建响应式管理界面
- ✅ 建立完整的API接口
